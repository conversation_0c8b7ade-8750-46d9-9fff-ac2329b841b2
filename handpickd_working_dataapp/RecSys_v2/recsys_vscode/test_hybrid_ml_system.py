#!/usr/bin/env python3
"""
🔥 TEST HYBRID ML-ENHANCED RECOMMENDATION SYSTEM

This script tests the hybrid approach that combines the proven Enhanced Temporal System
(30.3% precision) with selective ML enhancements that amplify existing signals.

HYBRID STRATEGY:
1. START with Enhanced Temporal System (30.3% precision) as strong base
2. ADD selective ML enhancements that AMPLIFY signals, don't replace them
3. Use ML for refinement: clustering, similarities, sequence intelligence, confidence
4. Conservative ML boosts (10-20%) to maintain signal strength
5. Focus on proven patterns with ML optimization

TARGET: Improve upon 30.3% precision through intelligent ML amplification
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from hybrid_ml_enhanced_system import HybridMLEnhancedSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the hybrid ML-enhanced recommendation system"""
    
    print("🔥 TESTING HYBRID ML-ENHANCED RECOMMENDATION SYSTEM")
    print("="*80)
    print("🎯 STRATEGY: Enhanced Temporal (30.3%) + Selective ML Intelligence")
    print("📊 APPROACH: Amplify proven signals with ML, don't replace them")
    print("🔬 ML ENHANCEMENTS:")
    print("   • User clustering for personalization")
    print("   • Product similarities for discovery")
    print("   • Sequence intelligence for temporal boosts")
    print("   • Confidence scoring for ranking")
    print("="*80)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    print(f"✅ Date range: {customer_orders_rc['delivery_date'].min().strftime('%Y-%m-%d')} to {customer_orders_rc['delivery_date'].max().strftime('%Y-%m-%d')}")
    
    # Initialize hybrid ML-enhanced system
    print("\n🔥 INITIALIZING HYBRID ML-ENHANCED SYSTEM...")
    hybrid_system = HybridMLEnhancedSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING HYBRID ML-ENHANCED SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    hybrid_metrics = hybrid_system.calculate_precision_recall_hybrid_ml(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Performance analysis
    print(f"\n📈 HYBRID ML-ENHANCED SYSTEM PERFORMANCE:")
    print(f"   🎯 Precision: {hybrid_metrics['avg_precision']:.4f} ({hybrid_metrics['avg_precision']*100:.1f}%)")
    print(f"   🎯 Recall: {hybrid_metrics['avg_recall']:.4f} ({hybrid_metrics['avg_recall']*100:.1f}%)")
    print(f"   🎯 F1-Score: {hybrid_metrics['avg_f1']:.4f} ({hybrid_metrics['avg_f1']*100:.1f}%)")
    
    # Compare with all previous systems
    baseline_precision = 0.22  # Original baseline
    enhanced_precision = 0.303  # Enhanced temporal system
    advanced_ml_precision = 0.0037  # Advanced ML (failed)
    ultra_precision = 0.305  # Ultra high precision
    
    print(f"\n📊 COMPREHENSIVE COMPARISON:")
    print(f"   📈 vs Baseline (~22%): {hybrid_metrics['avg_precision'] - baseline_precision:+.4f} ({(hybrid_metrics['avg_precision'] - baseline_precision)*100:+.1f}%)")
    print(f"   📈 vs Enhanced Temporal (30.3%): {hybrid_metrics['avg_precision'] - enhanced_precision:+.4f} ({(hybrid_metrics['avg_precision'] - enhanced_precision)*100:+.1f}%)")
    print(f"   📈 vs Advanced ML (0.4%): {hybrid_metrics['avg_precision'] - advanced_ml_precision:+.4f} ({(hybrid_metrics['avg_precision'] - advanced_ml_precision)*100:+.1f}%)")
    print(f"   📈 vs Ultra High (30.5%): {hybrid_metrics['avg_precision'] - ultra_precision:+.4f} ({(hybrid_metrics['avg_precision'] - ultra_precision)*100:+.1f}%)")
    
    # Target achievement analysis
    target_precision = 0.50
    target_recall = 0.50
    improvement_target = 0.35  # 35% as intermediate target
    
    precision_achieved = hybrid_metrics['avg_precision'] >= target_precision
    recall_achieved = hybrid_metrics['avg_recall'] >= target_recall
    improvement_achieved = hybrid_metrics['avg_precision'] >= improvement_target
    
    print(f"\n🎯 TARGET ACHIEVEMENT ANALYSIS:")
    print(f"   Ultimate 50% precision: {'✅ ACHIEVED' if precision_achieved else '❌ NOT ACHIEVED'} "
          f"({hybrid_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Ultimate 50% recall: {'✅ ACHIEVED' if recall_achieved else '❌ NOT ACHIEVED'} "
          f"({hybrid_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    print(f"   Improvement 35% target: {'✅ ACHIEVED' if improvement_achieved else '❌ NOT ACHIEVED'} "
          f"({hybrid_metrics['avg_precision']*100:.1f}% vs 35.0%)")
    
    # ML enhancement effectiveness analysis
    if 'ml_boost_correlation' in hybrid_metrics:
        ml_correlation = hybrid_metrics['ml_boost_correlation']
        ml_features = hybrid_metrics['ml_features_count']
        
        print(f"\n🔥 ML ENHANCEMENT EFFECTIVENESS:")
        print(f"   🤖 ML boost correlation with precision: {ml_correlation:.4f}")
        print(f"   👥 Users with ML clustering: {ml_features['user_clusters']:,}")
        print(f"   🎯 Products with ML similarities: {ml_features['product_similarities']:,}")
        print(f"   🔄 Users with sequence intelligence: {ml_features['sequence_intelligence']:,}")
        print(f"   📊 Users with confidence scoring: {ml_features['confidence_scores']:,}")
        
        if ml_correlation > 0.1:
            print(f"   ✅ ML enhancements showing positive correlation!")
        elif ml_correlation > 0:
            print(f"   📊 ML enhancements showing weak positive effect")
        else:
            print(f"   ⚠️ ML enhancements may need optimization")
    
    # Performance distribution analysis
    precision_scores = hybrid_metrics['precision_scores']
    recall_scores = hybrid_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"\n📊 HYBRID PERFORMANCE DISTRIBUTION:")
        
        # Precision distribution
        very_high_precision = sum(1 for p in precision_scores if p >= 0.7)
        high_precision = sum(1 for p in precision_scores if p >= 0.5)
        good_precision = sum(1 for p in precision_scores if p >= 0.35)
        medium_precision = sum(1 for p in precision_scores if 0.25 <= p < 0.35)
        
        print(f"   📈 Precision Distribution:")
        print(f"      Very High (≥70%): {very_high_precision:,} users ({very_high_precision/len(precision_scores)*100:.1f}%)")
        print(f"      High (≥50%): {high_precision:,} users ({high_precision/len(precision_scores)*100:.1f}%)")
        print(f"      Good (≥35%): {good_precision:,} users ({good_precision/len(precision_scores)*100:.1f}%)")
        print(f"      Medium (25-34%): {medium_precision:,} users ({medium_precision/len(precision_scores)*100:.1f}%)")
        
        # Both metrics analysis
        both_high = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.5 and r >= 0.5)
        both_good = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.35 and r >= 0.35)
        
        print(f"   🎯 Both precision AND recall:")
        print(f"      ≥50%: {both_high:,} users ({both_high/len(precision_scores)*100:.1f}%)")
        print(f"      ≥35%: {both_good:,} users ({both_good/len(precision_scores)*100:.1f}%)")
    
    # Sample hybrid ML recommendations
    print(f"\n🔍 SAMPLE HYBRID ML-ENHANCED RECOMMENDATIONS:")
    print("-"*70)
    
    # Get test users
    test_users = customer_orders_rc['customer_id'].value_counts().head(5).index.tolist()
    
    for i, user_id in enumerate(test_users[:3]):
        print(f"\n👤 User {i+1}: {user_id[:8]}...")
        
        # Generate hybrid ML recommendations
        recommendations = hybrid_system.get_hybrid_ml_recommendations(user_id, max_date)
        
        if recommendations:
            print(f"   🔥 Hybrid ML-enhanced recommendations ({len(recommendations)} items):")
            for j, rec in enumerate(recommendations):
                ml_info = f"C:{rec.get('ml_cluster_boost', 0):.2f} S:{rec.get('ml_similarity_boost', 0):.2f} Q:{rec.get('ml_sequence_boost', 0):.2f}"
                print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, ML:{ml_info}, Cat: {rec['category']})")
        else:
            print(f"   ⚠️ No recommendations generated")
    
    # Hybrid system effectiveness analysis
    print(f"\n💡 HYBRID SYSTEM EFFECTIVENESS ANALYSIS:")
    print("-"*70)
    
    if precision_achieved and recall_achieved:
        print(f"🎉 BREAKTHROUGH: 50%+ TARGET ACHIEVED WITH HYBRID ML!")
        print(f"   ✅ Enhanced Temporal base (30.3%) successfully amplified by ML")
        print(f"   🤖 Selective ML enhancements working perfectly")
        print(f"   🔥 Hybrid approach validated as optimal strategy")
        
    elif hybrid_metrics['avg_precision'] >= 0.40:
        print(f"📈 EXCELLENT PROGRESS: 40%+ precision with hybrid approach!")
        print(f"   🔥 Hybrid strategy showing strong results")
        print(f"   🤖 ML enhancements successfully amplifying base system")
        print(f"   🎯 Very close to 50% breakthrough target")
        
    elif hybrid_metrics['avg_precision'] >= 0.35:
        print(f"📊 SIGNIFICANT SUCCESS: 35%+ precision achieved!")
        print(f"   🔥 Hybrid approach outperforming pure ML")
        print(f"   📈 Meaningful improvement over enhanced temporal")
        print(f"   🤖 ML enhancements adding value")
        
    elif hybrid_metrics['avg_precision'] >= enhanced_precision:
        print(f"✅ MAINTAINED PERFORMANCE: Matching enhanced temporal system")
        print(f"   🔥 Hybrid approach not degrading proven performance")
        print(f"   🤖 ML enhancements need fine-tuning")
        print(f"   📊 Good foundation for further optimization")
        
    else:
        print(f"🔧 OPTIMIZATION NEEDED")
        print(f"   🔥 Hybrid approach needs refinement")
        print(f"   🤖 ML enhancements may be too aggressive")
        print(f"   📊 Consider reducing ML boost weights")
    
    # Business recommendations
    print(f"\n💼 BUSINESS RECOMMENDATIONS:")
    print("-"*70)
    
    total_users_tested = hybrid_metrics['num_users_tested']
    if precision_scores:
        effective_users = sum(1 for p in precision_scores if p >= 0.35)
        business_impact = effective_users / total_users_tested if total_users_tested > 0 else 0
        
        print(f"   📊 Users with 35%+ precision: {effective_users:,}/{total_users_tested:,} ({business_impact:.1%})")
        
        # ROI estimation
        if hybrid_metrics['avg_precision'] > enhanced_precision:
            improvement_factor = hybrid_metrics['avg_precision'] / enhanced_precision
            print(f"   💰 Hybrid improvement factor: {improvement_factor:.2f}x over enhanced temporal")
            print(f"   🔥 Hybrid ML ROI: {'Very High' if improvement_factor > 1.3 else 'High' if improvement_factor > 1.1 else 'Medium'}")
        
        # Deployment recommendation
        if precision_achieved and recall_achieved:
            print(f"   🚀 Recommendation: DEPLOY HYBRID ML IMMEDIATELY - Breakthrough achieved!")
        elif hybrid_metrics['avg_precision'] >= 0.40:
            print(f"   📊 Recommendation: DEPLOY HYBRID WITH MONITORING - Excellent performance")
        elif hybrid_metrics['avg_precision'] >= 0.35:
            print(f"   🔥 Recommendation: DEPLOY HYBRID SYSTEM - Significant improvement")
        elif hybrid_metrics['avg_precision'] >= enhanced_precision * 1.05:
            print(f"   📈 Recommendation: PILOT HYBRID APPROACH - Showing promise")
        else:
            print(f"   🔧 Recommendation: OPTIMIZE ML ENHANCEMENTS - Maintain enhanced temporal")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"🎉 HYBRID ML-ENHANCED SYSTEM TEST COMPLETE!")
    
    if precision_achieved and recall_achieved:
        print(f"✅ BREAKTHROUGH: 50%+ TARGET ACHIEVED WITH HYBRID ML!")
        print(f"🔥 Hybrid: Precision {hybrid_metrics['avg_precision']*100:.1f}% | Recall {hybrid_metrics['avg_recall']*100:.1f}%")
        print(f"🚀 READY FOR PRODUCTION - Hybrid ML breakthrough!")
        
    elif hybrid_metrics['avg_precision'] >= 0.40:
        print(f"📈 EXCELLENT SUCCESS: 40%+ precision with hybrid approach!")
        print(f"🔥 Hybrid: Precision {hybrid_metrics['avg_precision']*100:.1f}% | Recall {hybrid_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Close to 50% breakthrough target")
        
    elif hybrid_metrics['avg_precision'] >= 0.35:
        print(f"📊 SIGNIFICANT SUCCESS: 35%+ precision achieved!")
        print(f"🔥 Hybrid: Precision {hybrid_metrics['avg_precision']*100:.1f}% | Recall {hybrid_metrics['avg_recall']*100:.1f}%")
        print(f"🤖 ML enhancements adding meaningful value")
        
    elif hybrid_metrics['avg_precision'] >= enhanced_precision:
        print(f"✅ PERFORMANCE MAINTAINED: Matching enhanced temporal")
        print(f"🔥 Hybrid: Precision {hybrid_metrics['avg_precision']*100:.1f}% | Recall {hybrid_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 ML enhancements need optimization")
        
    else:
        print(f"🔧 OPTIMIZATION NEEDED")
        print(f"📊 Current Hybrid: Precision {hybrid_metrics['avg_precision']*100:.1f}% | Recall {hybrid_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*80)
    
    return hybrid_metrics

if __name__ == "__main__":
    results = main()
