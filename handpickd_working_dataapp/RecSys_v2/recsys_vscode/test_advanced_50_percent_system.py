#!/usr/bin/env python3
"""
🚀 TEST ADVANCED 50%+ PRECISION & RECALL SYSTEM

This script tests the advanced recommendation system targeting 50%+ precision AND recall,
building on deep pattern analysis insights and sophisticated algorithms.

ADVANCED FEATURES TESTED:
1. Precision Targeting (151 high-potential users: 60-70% precision expected)
2. Temporal Precision (100% users have temporal patterns)
3. Sequence Prediction (301 products with next-item patterns)
4. Quantity Awareness (55.3 avg consistent products per user)
5. Category Flow Prediction (Fruits↔Vegetables: 87.3%/84.6% confidence)
6. Advanced Collaborative Filtering (similarity threshold 0.2+)

TARGET: 50%+ precision AND 50%+ recall simultaneously
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from advanced_50_percent_system import Advanced50PercentSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the advanced 50%+ precision and recall system"""
    
    print("🚀 TESTING ADVANCED 50%+ PRECISION & RECALL SYSTEM")
    print("="*80)
    print("🎯 TARGET: 50%+ precision AND 50%+ recall simultaneously")
    print("📊 CURRENT BASELINE: 30.3% precision, 23.8% recall")
    print("🔬 APPROACH: Deep pattern analysis + advanced algorithms")
    print("="*80)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    print(f"✅ Date range: {customer_orders_rc['delivery_date'].min().strftime('%Y-%m-%d')} to {customer_orders_rc['delivery_date'].max().strftime('%Y-%m-%d')}")
    
    # Initialize advanced 50% system
    print("\n🚀 INITIALIZING ADVANCED 50%+ SYSTEM...")
    advanced_system = Advanced50PercentSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING ADVANCED 50%+ SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    advanced_metrics = advanced_system.calculate_precision_recall_50_percent(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Performance analysis
    print(f"\n📈 ADVANCED 50%+ SYSTEM PERFORMANCE:")
    print(f"   🎯 Precision: {advanced_metrics['avg_precision']:.4f} ({advanced_metrics['avg_precision']*100:.1f}%)")
    print(f"   🎯 Recall: {advanced_metrics['avg_recall']:.4f} ({advanced_metrics['avg_recall']*100:.1f}%)")
    print(f"   🎯 F1-Score: {advanced_metrics['avg_f1']:.4f} ({advanced_metrics['avg_f1']*100:.1f}%)")
    
    # Compare with baseline and enhanced system
    baseline_precision = 0.22  # Original baseline
    enhanced_precision = 0.303  # Enhanced system
    baseline_recall = 0.24
    enhanced_recall = 0.238
    
    precision_improvement_from_baseline = advanced_metrics['avg_precision'] - baseline_precision
    precision_improvement_from_enhanced = advanced_metrics['avg_precision'] - enhanced_precision
    recall_improvement_from_baseline = advanced_metrics['avg_recall'] - baseline_recall
    recall_improvement_from_enhanced = advanced_metrics['avg_recall'] - enhanced_recall
    
    print(f"\n📊 IMPROVEMENT ANALYSIS:")
    print(f"   📈 Precision vs Baseline (~22%): {precision_improvement_from_baseline:+.4f} ({precision_improvement_from_baseline*100:+.1f}%)")
    print(f"   📈 Precision vs Enhanced (30.3%): {precision_improvement_from_enhanced:+.4f} ({precision_improvement_from_enhanced*100:+.1f}%)")
    print(f"   📈 Recall vs Baseline (~24%): {recall_improvement_from_baseline:+.4f} ({recall_improvement_from_baseline*100:+.1f}%)")
    print(f"   📈 Recall vs Enhanced (23.8%): {recall_improvement_from_enhanced:+.4f} ({recall_improvement_from_enhanced*100:+.1f}%)")
    
    # Target achievement analysis
    target_precision = 0.50
    target_recall = 0.50
    
    precision_achieved = advanced_metrics['avg_precision'] >= target_precision
    recall_achieved = advanced_metrics['avg_recall'] >= target_recall
    
    print(f"\n🎯 TARGET ACHIEVEMENT ANALYSIS:")
    print(f"   Precision 50% target: {'✅ ACHIEVED' if precision_achieved else '❌ NOT ACHIEVED'} "
          f"({advanced_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Recall 50% target: {'✅ ACHIEVED' if recall_achieved else '❌ NOT ACHIEVED'} "
          f"({advanced_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    
    # Precision level analysis
    if 'level_metrics' in advanced_metrics:
        print(f"\n👥 PRECISION LEVEL PERFORMANCE:")
        level_metrics = advanced_metrics['level_metrics']
        
        for level in ['high', 'medium', 'low']:
            if level in level_metrics:
                metrics = level_metrics[level]
                print(f"   {level.upper()} precision users: {metrics['precision']:.4f} precision, "
                      f"{metrics['recall']:.4f} recall ({metrics['count']} users)")
                
                # Check if this level achieves 50% target
                level_precision_achieved = metrics['precision'] >= 0.50
                level_recall_achieved = metrics['recall'] >= 0.50
                if level_precision_achieved and level_recall_achieved:
                    print(f"      ✅ 50%+ TARGET ACHIEVED for {level} precision users!")
                elif level_precision_achieved or level_recall_achieved:
                    print(f"      📊 Partial success for {level} precision users")
    
    # Performance distribution analysis
    precision_scores = advanced_metrics['precision_scores']
    recall_scores = advanced_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"\n📊 PERFORMANCE DISTRIBUTION:")
        
        # Precision distribution
        high_precision_users = sum(1 for p in precision_scores if p >= 0.5)
        medium_precision_users = sum(1 for p in precision_scores if 0.3 <= p < 0.5)
        low_precision_users = sum(1 for p in precision_scores if p < 0.3)
        
        print(f"   📈 Precision Distribution:")
        print(f"      High (≥50%): {high_precision_users:,} users ({high_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_precision_users:,} users ({medium_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Low (<30%): {low_precision_users:,} users ({low_precision_users/len(precision_scores)*100:.1f}%)")
        
        # Recall distribution
        high_recall_users = sum(1 for r in recall_scores if r >= 0.5)
        medium_recall_users = sum(1 for r in recall_scores if 0.3 <= r < 0.5)
        low_recall_users = sum(1 for r in recall_scores if r < 0.3)
        
        print(f"   📈 Recall Distribution:")
        print(f"      High (≥50%): {high_recall_users:,} users ({high_recall_users/len(recall_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_recall_users:,} users ({medium_recall_users/len(recall_scores)*100:.1f}%)")
        print(f"      Low (<30%): {low_recall_users:,} users ({low_recall_users/len(recall_scores)*100:.1f}%)")
        
        # Both metrics high
        both_high_users = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.5 and r >= 0.5)
        print(f"   🎯 Both precision AND recall ≥50%: {both_high_users:,} users ({both_high_users/len(precision_scores)*100:.1f}%)")
    
    # Sample advanced recommendations
    print(f"\n🔍 SAMPLE ADVANCED 50%+ RECOMMENDATIONS:")
    print("-"*70)
    
    # Get test users from different precision levels
    test_users = customer_orders_rc['customer_id'].value_counts().head(5).index.tolist()
    
    for i, user_id in enumerate(test_users[:3]):
        print(f"\n👤 User {i+1}: {user_id[:8]}...")
        
        # Get user precision level
        user_potential = advanced_system.user_precision_potential.get(user_id, {'level': 'unknown', 'target_precision': 0.0})
        precision_level = user_potential['level']
        target_precision = user_potential.get('target_precision', 0.0)
        
        print(f"   🎯 Precision Level: {precision_level.upper()} (target: {target_precision:.1%})")
        
        # Generate advanced recommendations
        recommendations = advanced_system.get_advanced_50_percent_recommendations(user_id, max_date)
        
        print(f"   🚀 Advanced 50%+ recommendations ({len(recommendations)} items):")
        for j, rec in enumerate(recommendations):
            print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, "
                  f"Qty: {rec['predicted_quantity']}, Cat: {rec['category']})")
    
    # Algorithm effectiveness analysis
    print(f"\n💡 ALGORITHM EFFECTIVENESS ANALYSIS:")
    print("-"*70)
    
    if advanced_metrics['avg_precision'] >= 0.50 and advanced_metrics['avg_recall'] >= 0.50:
        print(f"🎉 BREAKTHROUGH SUCCESS: 50%+ TARGET ACHIEVED!")
        print(f"   ✅ Advanced algorithms successfully leveraged:")
        print(f"      • Precision targeting for high-potential users")
        print(f"      • Temporal patterns (100% user coverage)")
        print(f"      • Sequence prediction (301 products)")
        print(f"      • Quantity awareness (55.3 avg consistent products)")
        print(f"      • Category flow prediction")
        print(f"      • Advanced collaborative filtering")
        
    elif advanced_metrics['avg_precision'] >= 0.45 or advanced_metrics['avg_recall'] >= 0.45:
        print(f"📈 SIGNIFICANT PROGRESS: 45%+ ACCURACY ACHIEVED!")
        print(f"   🎯 Very close to 50% target:")
        print(f"      • Advanced pattern recognition working")
        print(f"      • Precision targeting effective")
        print(f"      • Minor fine-tuning needed")
        
    elif advanced_metrics['avg_precision'] >= 0.35 or advanced_metrics['avg_recall'] >= 0.35:
        print(f"📊 GOOD IMPROVEMENT: 35%+ ACCURACY ACHIEVED!")
        print(f"   🔧 Meaningful progress made:")
        print(f"      • Advanced algorithms showing impact")
        print(f"      • Further optimization needed")
        print(f"      • Consider ensemble weight adjustments")
        
    else:
        print(f"🔧 CONTINUED OPTIMIZATION NEEDED")
        print(f"   🔍 Advanced algorithms need refinement:")
        print(f"      • Review precision targeting thresholds")
        print(f"      • Adjust ensemble weights")
        print(f"      • Enhance pattern recognition")
    
    # Business impact assessment
    print(f"\n💼 BUSINESS IMPACT ASSESSMENT:")
    print("-"*70)
    
    total_users_tested = advanced_metrics['num_users_tested']
    if precision_scores:
        effective_users = sum(1 for p in precision_scores if p >= 0.4)
        business_impact = effective_users / total_users_tested if total_users_tested > 0 else 0
        
        print(f"   📊 Users with 40%+ precision: {effective_users:,}/{total_users_tested:,} ({business_impact:.1%})")
        
        # ROI estimation
        if advanced_metrics['avg_precision'] > enhanced_precision:
            improvement_factor = advanced_metrics['avg_precision'] / enhanced_precision
            print(f"   💰 Precision improvement factor: {improvement_factor:.2f}x over enhanced system")
            print(f"   📈 Business impact: {'Very High' if improvement_factor > 1.5 else 'High' if improvement_factor > 1.2 else 'Medium'}")
        
        # Deployment recommendation
        if advanced_metrics['avg_precision'] >= 0.50 and advanced_metrics['avg_recall'] >= 0.50:
            print(f"   🚀 Recommendation: DEPLOY IMMEDIATELY - 50%+ target achieved!")
        elif advanced_metrics['avg_precision'] >= 0.45:
            print(f"   📊 Recommendation: DEPLOY WITH MONITORING - Excellent improvement")
        elif advanced_metrics['avg_precision'] >= 0.35:
            print(f"   🔧 Recommendation: FURTHER OPTIMIZATION - Good progress made")
        else:
            print(f"   🔍 Recommendation: CONTINUE RESEARCH - More work needed")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"🎉 ADVANCED 50%+ SYSTEM TEST COMPLETE!")
    
    if precision_achieved and recall_achieved:
        print(f"✅ BREAKTHROUGH SUCCESS: 50%+ TARGET ACHIEVED!")
        print(f"🚀 Precision: {advanced_metrics['avg_precision']*100:.1f}% | Recall: {advanced_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 READY FOR IMMEDIATE PRODUCTION DEPLOYMENT")
        
    elif advanced_metrics['avg_precision'] >= 0.45 or advanced_metrics['avg_recall'] >= 0.45:
        print(f"📈 EXCELLENT PROGRESS: 45%+ ACCURACY ACHIEVED!")
        print(f"🚀 Precision: {advanced_metrics['avg_precision']*100:.1f}% | Recall: {advanced_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Minor optimizations needed for 50% target")
        
    elif advanced_metrics['avg_precision'] >= 0.35 or advanced_metrics['avg_recall'] >= 0.35:
        print(f"📊 SIGNIFICANT IMPROVEMENT: 35%+ ACCURACY ACHIEVED!")
        print(f"🚀 Precision: {advanced_metrics['avg_precision']*100:.1f}% | Recall: {advanced_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Further optimization required for 50% target")
        
    else:
        print(f"🔧 CONTINUED RESEARCH NEEDED")
        print(f"📊 Current: Precision {advanced_metrics['avg_precision']*100:.1f}% | Recall {advanced_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*80)
    
    return advanced_metrics

if __name__ == "__main__":
    results = main()
