#!/usr/bin/env python3
"""
🔬 ADVANCED EDA & PATTERN DISCOVERY FOR RECOMMENDATION SYSTEM OPTIMIZATION

This script conducts deep analysis to understand why current system shows 22-24% accuracy
and identifies patterns to achieve 50%+ precision/recall in a constrained SKU environment.

ANALYSIS PHASES:
1. Customer Order Concentration Analysis
2. Customer Purchasing Archetypes/Clusters  
3. Temporal Pattern Discovery
4. Product Co-occurrence & Basket Analysis
5. Category Predictability Analysis
6. Order Size vs Accuracy Relationship
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

class AdvancedEDAAnalyzer:
    """Advanced EDA analyzer for recommendation system optimization"""
    
    def __init__(self):
        self.customer_orders_df = None
        self.catalogue_df = None
        self.sku_to_name_mapping = {}
        self.name_to_sku_mapping = {}
        
    def load_data(self):
        """Load and prepare data for analysis"""
        print("🔬 ADVANCED EDA & PATTERN DISCOVERY")
        print("="*70)
        print("🎯 GOAL: Identify patterns to achieve 50%+ precision/recall")
        print("📊 CURRENT: ~22-24% accuracy needs improvement")
        print("="*70)
        
        # Load data
        self.catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
        self.customer_orders_df = pd.read_csv('customer_orders.csv')
        
        # Convert delivery_date to datetime
        self.customer_orders_df['delivery_date'] = pd.to_datetime(self.customer_orders_df['delivery_date'])
        
        # Create mappings
        for _, row in self.catalogue_df.iterrows():
            product_name = row['name']
            sku_code = row['sku_code']
            self.sku_to_name_mapping[sku_code] = product_name
            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)
        
        # Add product names to orders
        self.customer_orders_df['product_name'] = self.customer_orders_df['sku_code'].map(self.sku_to_name_mapping)
        self.customer_orders_df = self.customer_orders_df.dropna(subset=['product_name'])
        
        print(f"✅ Loaded {len(self.customer_orders_df):,} orders")
        print(f"✅ Loaded {len(self.catalogue_df):,} products")
        print(f"✅ Date range: {self.customer_orders_df['delivery_date'].min().strftime('%Y-%m-%d')} to {self.customer_orders_df['delivery_date'].max().strftime('%Y-%m-%d')}")
        
    def analyze_customer_order_concentration(self):
        """Phase 1: Analyze customer order concentration patterns"""
        print(f"\n📊 PHASE 1: CUSTOMER ORDER CONCENTRATION ANALYSIS")
        print("-"*50)
        
        # Calculate customer metrics
        customer_metrics = []
        
        for customer_id, customer_orders in self.customer_orders_df.groupby('customer_id'):
            unique_skus = customer_orders['product_name'].nunique()
            total_orders = len(customer_orders)
            unique_order_sessions = customer_orders['display_order_id'].nunique()
            avg_items_per_session = total_orders / unique_order_sessions if unique_order_sessions > 0 else 0
            
            # Time span analysis
            date_range = (customer_orders['delivery_date'].max() - customer_orders['delivery_date'].min()).days
            
            # Category diversity
            categories = customer_orders.merge(self.catalogue_df[['sku_code', 'category_name']], on='sku_code', how='left')
            unique_categories = categories['category_name'].nunique()
            
            # Frequency analysis
            product_frequencies = customer_orders['product_name'].value_counts()
            top_product_share = product_frequencies.iloc[0] / total_orders if len(product_frequencies) > 0 else 0
            
            customer_metrics.append({
                'customer_id': customer_id,
                'unique_skus': unique_skus,
                'total_orders': total_orders,
                'unique_order_sessions': unique_order_sessions,
                'avg_items_per_session': avg_items_per_session,
                'date_range_days': date_range,
                'unique_categories': unique_categories,
                'top_product_share': top_product_share,
                'sku_concentration': unique_skus / total_orders if total_orders > 0 else 0
            })
        
        customer_metrics_df = pd.DataFrame(customer_metrics)
        
        print(f"📈 CUSTOMER CONCENTRATION INSIGHTS:")
        print(f"   Average unique SKUs per customer: {customer_metrics_df['unique_skus'].mean():.1f}")
        print(f"   Median unique SKUs per customer: {customer_metrics_df['unique_skus'].median():.1f}")
        print(f"   Average items per order session: {customer_metrics_df['avg_items_per_session'].mean():.1f}")
        print(f"   Average top product share: {customer_metrics_df['top_product_share'].mean():.2%}")
        print(f"   Average category diversity: {customer_metrics_df['unique_categories'].mean():.1f}")
        
        # Concentration segments
        customer_metrics_df['concentration_segment'] = pd.cut(
            customer_metrics_df['unique_skus'], 
            bins=[0, 10, 25, 50, float('inf')], 
            labels=['Focused (≤10)', 'Moderate (11-25)', 'Diverse (26-50)', 'Very Diverse (>50)']
        )
        
        concentration_dist = customer_metrics_df['concentration_segment'].value_counts()
        print(f"\n📊 CUSTOMER CONCENTRATION SEGMENTS:")
        for segment, count in concentration_dist.items():
            pct = count / len(customer_metrics_df) * 100
            print(f"   {segment}: {count:,} customers ({pct:.1f}%)")
        
        return customer_metrics_df
    
    def analyze_purchasing_archetypes(self, customer_metrics_df):
        """Phase 2: Identify customer purchasing archetypes/clusters"""
        print(f"\n🎭 PHASE 2: CUSTOMER PURCHASING ARCHETYPES ANALYSIS")
        print("-"*50)
        
        # Prepare features for clustering
        clustering_features = [
            'unique_skus', 'avg_items_per_session', 'unique_categories', 
            'top_product_share', 'sku_concentration'
        ]
        
        # Handle any missing values
        feature_data = customer_metrics_df[clustering_features].fillna(0)
        
        # Standardize features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(feature_data)
        
        # Perform clustering
        n_clusters = 5
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        customer_metrics_df['archetype'] = kmeans.fit_predict(scaled_features)
        
        # Analyze archetypes
        print(f"📊 CUSTOMER ARCHETYPES IDENTIFIED:")
        for archetype in range(n_clusters):
            archetype_data = customer_metrics_df[customer_metrics_df['archetype'] == archetype]
            count = len(archetype_data)
            pct = count / len(customer_metrics_df) * 100
            
            print(f"\n   🎭 Archetype {archetype} ({count:,} customers, {pct:.1f}%):")
            print(f"      Avg unique SKUs: {archetype_data['unique_skus'].mean():.1f}")
            print(f"      Avg items/session: {archetype_data['avg_items_per_session'].mean():.1f}")
            print(f"      Avg categories: {archetype_data['unique_categories'].mean():.1f}")
            print(f"      Top product share: {archetype_data['top_product_share'].mean():.2%}")
        
        return customer_metrics_df
    
    def analyze_temporal_patterns(self):
        """Phase 3: Examine temporal patterns"""
        print(f"\n⏰ PHASE 3: TEMPORAL PATTERN DISCOVERY")
        print("-"*50)
        
        # Add temporal features
        orders_temporal = self.customer_orders_df.copy()
        orders_temporal['day_of_week'] = orders_temporal['delivery_date'].dt.day_name()
        orders_temporal['month'] = orders_temporal['delivery_date'].dt.month
        orders_temporal['week_of_year'] = orders_temporal['delivery_date'].dt.isocalendar().week
        
        # Day of week analysis
        dow_patterns = orders_temporal.groupby(['customer_id', 'day_of_week']).size().unstack(fill_value=0)
        dow_preferences = dow_patterns.div(dow_patterns.sum(axis=1), axis=0)
        
        print(f"📅 DAY-OF-WEEK PATTERNS:")
        overall_dow = orders_temporal['day_of_week'].value_counts()
        for day, count in overall_dow.items():
            pct = count / len(orders_temporal) * 100
            print(f"   {day}: {pct:.1f}% of orders")
        
        # Reorder interval analysis
        print(f"\n🔄 REORDER INTERVAL ANALYSIS:")
        reorder_intervals = []
        
        for customer_id, customer_orders in orders_temporal.groupby('customer_id'):
            for product_name, product_orders in customer_orders.groupby('product_name'):
                if len(product_orders) >= 2:
                    sorted_orders = product_orders.sort_values('delivery_date')
                    for i in range(1, len(sorted_orders)):
                        interval = (sorted_orders.iloc[i]['delivery_date'] - 
                                  sorted_orders.iloc[i-1]['delivery_date']).days
                        reorder_intervals.append({
                            'customer_id': customer_id,
                            'product_name': product_name,
                            'interval_days': interval
                        })
        
        if reorder_intervals:
            intervals_df = pd.DataFrame(reorder_intervals)
            print(f"   Average reorder interval: {intervals_df['interval_days'].mean():.1f} days")
            print(f"   Median reorder interval: {intervals_df['interval_days'].median():.1f} days")
            print(f"   Most common intervals: {intervals_df['interval_days'].value_counts().head(5).to_dict()}")
        
        return orders_temporal, intervals_df if reorder_intervals else None
    
    def analyze_product_cooccurrence(self):
        """Phase 4: Product co-occurrence & basket analysis"""
        print(f"\n🛒 PHASE 4: PRODUCT CO-OCCURRENCE & BASKET ANALYSIS")
        print("-"*50)
        
        # Basket analysis by order session
        basket_data = []
        cooccurrence_matrix = defaultdict(lambda: defaultdict(int))
        
        for order_id, order_items in self.customer_orders_df.groupby('display_order_id'):
            products = order_items['product_name'].tolist()
            basket_size = len(products)
            
            basket_data.append({
                'order_id': order_id,
                'basket_size': basket_size,
                'products': products
            })
            
            # Build co-occurrence matrix
            for i, product_a in enumerate(products):
                for j, product_b in enumerate(products):
                    if i != j:
                        cooccurrence_matrix[product_a][product_b] += 1
        
        baskets_df = pd.DataFrame(basket_data)
        
        print(f"🛒 BASKET ANALYSIS:")
        print(f"   Average basket size: {baskets_df['basket_size'].mean():.1f} items")
        print(f"   Median basket size: {baskets_df['basket_size'].median():.1f} items")
        print(f"   Basket size distribution: {baskets_df['basket_size'].value_counts().head(10).to_dict()}")
        
        # Find top co-occurring product pairs
        cooccurrence_pairs = []
        for product_a, related_products in cooccurrence_matrix.items():
            for product_b, count in related_products.items():
                if count >= 5:  # Minimum co-occurrence threshold
                    cooccurrence_pairs.append({
                        'product_a': product_a,
                        'product_b': product_b,
                        'cooccurrence_count': count
                    })
        
        if cooccurrence_pairs:
            cooccurrence_df = pd.DataFrame(cooccurrence_pairs)
            cooccurrence_df = cooccurrence_df.sort_values('cooccurrence_count', ascending=False)
            
            print(f"\n🔗 TOP PRODUCT CO-OCCURRENCES:")
            for _, row in cooccurrence_df.head(10).iterrows():
                print(f"   {row['product_a']} + {row['product_b']}: {row['cooccurrence_count']} times")
        
        return baskets_df, cooccurrence_df if cooccurrence_pairs else None
    
    def analyze_category_predictability(self):
        """Phase 5: Category predictability analysis"""
        print(f"\n📂 PHASE 5: CATEGORY PREDICTABILITY ANALYSIS")
        print("-"*50)
        
        # Merge with category information
        orders_with_categories = self.customer_orders_df.merge(
            self.catalogue_df[['sku_code', 'category_name']], 
            on='sku_code', 
            how='left'
        )
        
        # Category frequency analysis
        category_stats = []
        
        for category, category_orders in orders_with_categories.groupby('category_name'):
            unique_customers = category_orders['customer_id'].nunique()
            total_orders = len(category_orders)
            unique_products = category_orders['product_name'].nunique()
            avg_orders_per_customer = total_orders / unique_customers if unique_customers > 0 else 0
            
            # Calculate repeat purchase rate within category
            customer_repeat_rates = []
            for customer_id, customer_category_orders in category_orders.groupby('customer_id'):
                if len(customer_category_orders) > 1:
                    customer_repeat_rates.append(1)
                else:
                    customer_repeat_rates.append(0)
            
            repeat_rate = np.mean(customer_repeat_rates) if customer_repeat_rates else 0
            
            category_stats.append({
                'category_name': category,
                'unique_customers': unique_customers,
                'total_orders': total_orders,
                'unique_products': unique_products,
                'avg_orders_per_customer': avg_orders_per_customer,
                'repeat_purchase_rate': repeat_rate,
                'predictability_score': repeat_rate * avg_orders_per_customer
            })
        
        category_stats_df = pd.DataFrame(category_stats)
        category_stats_df = category_stats_df.sort_values('predictability_score', ascending=False)
        
        print(f"📊 CATEGORY PREDICTABILITY RANKING:")
        for _, row in category_stats_df.head(10).iterrows():
            print(f"   {row['category_name']}: Score {row['predictability_score']:.2f} "
                  f"(Repeat: {row['repeat_purchase_rate']:.1%}, Avg Orders: {row['avg_orders_per_customer']:.1f})")
        
        return category_stats_df
    
    def analyze_order_size_accuracy_relationship(self, customer_metrics_df):
        """Phase 6: Order size vs accuracy relationship"""
        print(f"\n📏 PHASE 6: ORDER SIZE VS ACCURACY RELATIONSHIP")
        print("-"*50)
        
        # Analyze relationship between order patterns and potential accuracy
        accuracy_indicators = []
        
        for customer_id, customer_orders in self.customer_orders_df.groupby('customer_id'):
            # Get customer metrics
            customer_metric = customer_metrics_df[customer_metrics_df['customer_id'] == customer_id].iloc[0]
            
            # Calculate predictability indicators
            product_frequencies = customer_orders['product_name'].value_counts()
            top_3_share = product_frequencies.head(3).sum() / len(customer_orders) if len(product_frequencies) > 0 else 0
            
            # Order session analysis
            session_sizes = customer_orders.groupby('display_order_id').size()
            consistent_order_size = session_sizes.std() < 2  # Low variation in order size
            
            # Temporal consistency
            recent_orders = customer_orders[customer_orders['delivery_date'] >= 
                                         customer_orders['delivery_date'].max() - timedelta(days=30)]
            recent_products = set(recent_orders['product_name'].unique())
            historical_products = set(customer_orders['product_name'].unique())
            
            product_consistency = len(recent_products.intersection(historical_products)) / len(historical_products) if historical_products else 0
            
            # Predictability score
            predictability_score = (
                top_3_share * 0.4 +  # Top products dominance
                (1 - customer_metric['sku_concentration']) * 0.3 +  # Focus vs diversity
                product_consistency * 0.3  # Temporal consistency
            )
            
            accuracy_indicators.append({
                'customer_id': customer_id,
                'avg_order_size': customer_metric['avg_items_per_session'],
                'top_3_share': top_3_share,
                'consistent_order_size': consistent_order_size,
                'product_consistency': product_consistency,
                'predictability_score': predictability_score,
                'archetype': customer_metric['archetype']
            })
        
        accuracy_df = pd.DataFrame(accuracy_indicators)
        
        print(f"🎯 PREDICTABILITY INSIGHTS:")
        print(f"   Average predictability score: {accuracy_df['predictability_score'].mean():.3f}")
        print(f"   High predictability customers (>0.7): {(accuracy_df['predictability_score'] > 0.7).sum():,} "
              f"({(accuracy_df['predictability_score'] > 0.7).mean():.1%})")
        
        # Analyze by archetype
        print(f"\n📊 PREDICTABILITY BY ARCHETYPE:")
        for archetype in sorted(accuracy_df['archetype'].unique()):
            archetype_data = accuracy_df[accuracy_df['archetype'] == archetype]
            avg_predictability = archetype_data['predictability_score'].mean()
            print(f"   Archetype {archetype}: {avg_predictability:.3f} avg predictability")
        
        return accuracy_df
    
    def generate_insights_summary(self, customer_metrics_df, category_stats_df, accuracy_df):
        """Generate comprehensive insights summary"""
        print(f"\n💡 COMPREHENSIVE INSIGHTS SUMMARY")
        print("="*70)
        
        # Key findings
        total_customers = len(customer_metrics_df)
        high_predictability = (accuracy_df['predictability_score'] > 0.7).sum()
        focused_customers = (customer_metrics_df['unique_skus'] <= 25).sum()
        
        print(f"🔍 KEY FINDINGS FOR 50%+ ACCURACY:")
        print(f"   📊 {focused_customers:,}/{total_customers:,} customers ({focused_customers/total_customers:.1%}) are focused (≤25 SKUs)")
        print(f"   🎯 {high_predictability:,}/{total_customers:,} customers ({high_predictability/total_customers:.1%}) have high predictability")
        print(f"   📈 Top categories show {category_stats_df['repeat_purchase_rate'].head(5).mean():.1%} repeat rate")
        print(f"   🛒 Average basket size: {customer_metrics_df['avg_items_per_session'].mean():.1f} items")
        
        # Recommendations for improvement
        print(f"\n🚀 RECOMMENDATIONS FOR 50%+ ACCURACY:")
        print(f"   1. Focus on high-predictability customers first ({high_predictability:,} customers)")
        print(f"   2. Implement category-specific recommendation strategies")
        print(f"   3. Use customer archetype-based approaches")
        print(f"   4. Leverage product co-occurrence patterns")
        print(f"   5. Optimize for focused customers with consistent patterns")
        
        return {
            'total_customers': total_customers,
            'high_predictability_customers': high_predictability,
            'focused_customers': focused_customers,
            'avg_predictability': accuracy_df['predictability_score'].mean(),
            'top_category_repeat_rate': category_stats_df['repeat_purchase_rate'].head(5).mean()
        }

def main():
    """Main execution function"""
    analyzer = AdvancedEDAAnalyzer()
    
    # Load data
    analyzer.load_data()
    
    # Phase 1: Customer concentration
    customer_metrics_df = analyzer.analyze_customer_order_concentration()
    
    # Phase 2: Customer archetypes
    customer_metrics_df = analyzer.analyze_purchasing_archetypes(customer_metrics_df)
    
    # Phase 3: Temporal patterns
    orders_temporal, intervals_df = analyzer.analyze_temporal_patterns()
    
    # Phase 4: Product co-occurrence
    baskets_df, cooccurrence_df = analyzer.analyze_product_cooccurrence()
    
    # Phase 5: Category predictability
    category_stats_df = analyzer.analyze_category_predictability()
    
    # Phase 6: Order size vs accuracy
    accuracy_df = analyzer.analyze_order_size_accuracy_relationship(customer_metrics_df)
    
    # Generate insights summary
    insights = analyzer.generate_insights_summary(customer_metrics_df, category_stats_df, accuracy_df)
    
    print(f"\n✅ ADVANCED EDA ANALYSIS COMPLETE!")
    print(f"🎯 Ready to implement enhanced recommendation system for 50%+ accuracy")
    
    return {
        'customer_metrics_df': customer_metrics_df,
        'category_stats_df': category_stats_df,
        'accuracy_df': accuracy_df,
        'cooccurrence_df': cooccurrence_df,
        'insights': insights
    }

if __name__ == "__main__":
    results = main()
