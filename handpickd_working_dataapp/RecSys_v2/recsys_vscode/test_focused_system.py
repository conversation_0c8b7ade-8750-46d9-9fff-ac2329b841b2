#!/usr/bin/env python3
"""
🎯 TEST FOCUSED HIGH-PERFORMANCE RECOMMENDATION SYSTEM

This system focuses on proven high-impact patterns:
1. Recent Purchase Amplification (last 30 days get 5x weight)
2. Reorder Cycle Prediction (7-day median interval insight)
3. Category Stickiness (99.8% repeat rate)
4. Product Frequency in Limited SKU Space
5. Simple but Powerful Scoring

TARGET: 50%+ precision/recall through focused approach
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from focused_high_performance_system import FocusedHighPerformanceSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the focused high-performance recommendation system"""
    
    print("🎯 TESTING FOCUSED HIGH-PERFORMANCE RECOMMENDATION SYSTEM")
    print("="*80)
    print("🚀 APPROACH: Focus on proven high-impact patterns")
    print("📊 TARGET: 50%+ precision and recall")
    print("🔬 STRATEGY: Simple but powerful scoring")
    print("="*80)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    
    # Initialize focused system
    print("\n🎯 INITIALIZING FOCUSED HIGH-PERFORMANCE SYSTEM...")
    focused_system = FocusedHighPerformanceSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING FOCUSED SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    focused_metrics = focused_system.calculate_precision_recall_focused(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Performance analysis
    print(f"\n📈 FOCUSED SYSTEM PERFORMANCE:")
    print(f"   🎯 Precision: {focused_metrics['avg_precision']:.4f} ({focused_metrics['avg_precision']*100:.1f}%)")
    print(f"   🎯 Recall: {focused_metrics['avg_recall']:.4f} ({focused_metrics['avg_recall']*100:.1f}%)")
    print(f"   🎯 F1-Score: {focused_metrics['avg_f1']:.4f} ({focused_metrics['avg_f1']*100:.1f}%)")
    
    # Check target achievement
    target_precision = 0.50
    target_recall = 0.50
    
    precision_achieved = focused_metrics['avg_precision'] >= target_precision
    recall_achieved = focused_metrics['avg_recall'] >= target_recall
    
    print(f"\n🎯 TARGET ACHIEVEMENT ANALYSIS:")
    print(f"   Precision Target (50%): {'✅ ACHIEVED' if precision_achieved else '❌ NOT ACHIEVED'} "
          f"({focused_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Recall Target (50%): {'✅ ACHIEVED' if recall_achieved else '❌ NOT ACHIEVED'} "
          f"({focused_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    
    # Detailed performance distribution
    precision_scores = focused_metrics['precision_scores']
    recall_scores = focused_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"\n📊 PERFORMANCE DISTRIBUTION ANALYSIS:")
        
        # Precision analysis
        high_precision_users = sum(1 for p in precision_scores if p >= 0.5)
        medium_precision_users = sum(1 for p in precision_scores if 0.3 <= p < 0.5)
        low_precision_users = sum(1 for p in precision_scores if p < 0.3)
        
        print(f"   📈 Precision Distribution:")
        print(f"      High (≥50%): {high_precision_users:,} users ({high_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_precision_users:,} users ({medium_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Low (<30%): {low_precision_users:,} users ({low_precision_users/len(precision_scores)*100:.1f}%)")
        
        # Recall analysis
        high_recall_users = sum(1 for r in recall_scores if r >= 0.5)
        medium_recall_users = sum(1 for r in recall_scores if 0.3 <= r < 0.5)
        low_recall_users = sum(1 for r in recall_scores if r < 0.3)
        
        print(f"   📈 Recall Distribution:")
        print(f"      High (≥50%): {high_recall_users:,} users ({high_recall_users/len(recall_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_recall_users:,} users ({medium_recall_users/len(recall_scores)*100:.1f}%)")
        print(f"      Low (<30%): {low_recall_users:,} users ({low_recall_users/len(recall_scores)*100:.1f}%)")
        
        # Statistical insights
        print(f"\n📊 STATISTICAL INSIGHTS:")
        print(f"   Precision: Min={min(precision_scores):.3f}, Max={max(precision_scores):.3f}, Std={np.std(precision_scores):.3f}")
        print(f"   Recall: Min={min(recall_scores):.3f}, Max={max(recall_scores):.3f}, Std={np.std(recall_scores):.3f}")
    
    # Sample recommendations analysis
    print(f"\n🔍 SAMPLE FOCUSED RECOMMENDATIONS ANALYSIS:")
    print("-"*60)
    
    # Get test users with different performance levels
    test_users = customer_orders_rc['customer_id'].value_counts().head(10).index.tolist()
    
    for i, user_id in enumerate(test_users[:3]):
        print(f"\n👤 User {i+1}: {user_id[:8]}...")
        
        # Get user's historical context
        user_orders = customer_orders_rc[customer_orders_rc['customer_id'] == user_id]
        
        if len(user_orders) > 0:
            # Recent purchases (last 30 days)
            recent_cutoff = max_date - timedelta(days=30)
            recent_orders = user_orders[user_orders['delivery_date'] >= recent_cutoff]
            
            # Map to product names
            sku_to_name = dict(zip(catalogue_rc['sku_code'], catalogue_rc['name']))
            recent_products = recent_orders['sku_code'].map(sku_to_name).dropna().unique()
            
            # Categories
            recent_categories = set()
            for sku in recent_orders['sku_code'].unique():
                if sku in sku_to_name:
                    product_name = sku_to_name[sku]
                    category_row = catalogue_rc[catalogue_rc['name'] == product_name]
                    if not category_row.empty:
                        recent_categories.add(category_row.iloc[0]['category_name'])
            
            print(f"   📅 Recent purchases (30 days): {len(recent_products)} products")
            print(f"   📂 Recent categories: {list(recent_categories)}")
            
            # Generate focused recommendations
            recommendations = focused_system.get_focused_recommendations(user_id, max_date)
            
            print(f"   🎯 Focused recommendations ({len(recommendations)} items):")
            for j, rec in enumerate(recommendations):
                print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, Cat: {rec['category']})")
        else:
            print(f"   ⚠️ No historical data found")
    
    # Performance improvement analysis
    print(f"\n💡 PERFORMANCE IMPROVEMENT ANALYSIS:")
    print("-"*60)
    
    if focused_metrics['avg_precision'] >= 0.50:
        print(f"🎉 EXCELLENT: 50%+ PRECISION TARGET ACHIEVED!")
        print(f"   ✅ Focused approach successfully leveraged:")
        print(f"      • Recent purchase patterns (30-day focus)")
        print(f"      • Reorder cycle predictions (7-day median)")
        print(f"      • Category loyalty (99.8% repeat rate)")
        print(f"      • Product popularity in limited SKU space")
        
    elif focused_metrics['avg_precision'] >= 0.40:
        print(f"📈 VERY GOOD: 40%+ PRECISION ACHIEVED!")
        print(f"   🎯 Close to target - minor optimizations needed:")
        print(f"      • Fine-tune recent purchase weighting")
        print(f"      • Enhance reorder cycle detection")
        print(f"      • Optimize category loyalty scoring")
        
    elif focused_metrics['avg_precision'] >= 0.30:
        print(f"📊 GOOD IMPROVEMENT: 30%+ PRECISION ACHIEVED!")
        print(f"   🔧 Significant progress - further optimization:")
        print(f"      • Increase recent purchase amplification")
        print(f"      • Refine reorder probability calculations")
        print(f"      • Strengthen category-based recommendations")
        
    else:
        print(f"⚠️ NEEDS FURTHER OPTIMIZATION")
        print(f"   🔍 Root cause analysis required:")
        print(f"      • Validate feature computation logic")
        print(f"      • Check data quality and coverage")
        print(f"      • Review scoring weight distribution")
    
    # Business impact assessment
    print(f"\n💼 BUSINESS IMPACT ASSESSMENT:")
    print("-"*60)
    
    total_users_tested = focused_metrics['num_users_tested']
    if precision_scores:
        effective_users = sum(1 for p in precision_scores if p >= 0.3)
        business_impact = effective_users / total_users_tested if total_users_tested > 0 else 0
        
        print(f"   📊 Users with 30%+ precision: {effective_users:,}/{total_users_tested:,} ({business_impact:.1%})")
        print(f"   💰 Estimated business impact: {'High' if business_impact > 0.6 else 'Medium' if business_impact > 0.3 else 'Low'}")
        print(f"   🚀 Recommendation: {'Deploy immediately' if focused_metrics['avg_precision'] >= 0.4 else 'Further optimize before deployment'}")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"🎉 FOCUSED HIGH-PERFORMANCE SYSTEM TEST COMPLETE!")
    
    if precision_achieved and recall_achieved:
        print(f"✅ SUCCESS: 50%+ TARGET ACHIEVED FOR BOTH METRICS!")
        print(f"🚀 Precision: {focused_metrics['avg_precision']*100:.1f}% | Recall: {focused_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 READY FOR PRODUCTION DEPLOYMENT")
        
    elif focused_metrics['avg_precision'] >= 0.40 or focused_metrics['avg_recall'] >= 0.40:
        print(f"📈 SIGNIFICANT SUCCESS: 40%+ ACCURACY ACHIEVED!")
        print(f"🚀 Precision: {focused_metrics['avg_precision']*100:.1f}% | Recall: {focused_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Minor optimizations needed to reach 50% target")
        
    elif focused_metrics['avg_precision'] >= 0.30 or focused_metrics['avg_recall'] >= 0.30:
        print(f"📊 GOOD PROGRESS: 30%+ ACCURACY ACHIEVED!")
        print(f"🚀 Precision: {focused_metrics['avg_precision']*100:.1f}% | Recall: {focused_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Further optimization required for 50% target")
        
    else:
        print(f"🔧 CONTINUED OPTIMIZATION NEEDED")
        print(f"📊 Current: Precision {focused_metrics['avg_precision']*100:.1f}% | Recall {focused_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*80)
    
    return focused_metrics

if __name__ == "__main__":
    results = main()
