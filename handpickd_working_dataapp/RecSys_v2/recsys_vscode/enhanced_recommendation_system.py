#!/usr/bin/env python3
"""
🚀 ENHANCED RECOMMENDATION SYSTEM FOR 50%+ PRECISION/RECALL

Based on advanced EDA insights, this system implements:
1. Category-aware recommendations (99.8% repeat rate)
2. Product co-occurrence patterns (strong associations)
3. Customer archetype-specific strategies
4. Collaborative filtering with similar customers
5. Ensemble approach combining multiple methods

TARGET: Achieve 50%+ precision and recall
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class EnhancedRecommendationSystem:
    """Enhanced recommendation system targeting 50%+ accuracy"""
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        self.product_name_column = product_name_column
        
        print(f"🚀 ENHANCED RECOMMENDATION SYSTEM INITIALIZED")
        print(f"🎯 TARGET: 50%+ precision and recall")
        print(f"📊 APPROACH: Multi-strategy ensemble system")
        
        # Data structures
        self.item_categories = {}
        self.catalogue_lookup = {}
        self.name_to_sku_mapping = {}
        self.sku_to_name_mapping = {}
        
        # Enhanced structures
        self.customer_archetypes = {}
        self.product_cooccurrence = defaultdict(lambda: defaultdict(float))
        self.category_affinity = defaultdict(lambda: defaultdict(float))
        self.customer_similarity_matrix = {}
        self.reorder_patterns = {}
        
        # User repurchase ratios
        self.user_repurchase_ratios = {}
        self.default_repurchase_ratio = 0.7
        
        if user_repurchase_ratios_df is not None:
            self.load_user_repurchase_ratios(user_repurchase_ratios_df)
    
    def load_user_repurchase_ratios(self, repurchase_ratios_df):
        """Load user-specific repurchase ratios"""
        self.user_repurchase_ratios = dict(zip(
            repurchase_ratios_df['customer_id'], 
            repurchase_ratios_df['repurchase_ratio']
        ))
        print(f"✅ Loaded repurchase ratios for {len(self.user_repurchase_ratios):,} users")
    
    def precompute_enhanced_features(self, customer_orders_df, catalogue_df):
        """Pre-compute enhanced features for 50%+ accuracy"""
        print(f"🔧 PRE-COMPUTING ENHANCED FEATURES...")
        
        # Basic mappings
        for _, row in catalogue_df.iterrows():
            product_name = row[self.product_name_column]
            sku_code = row['sku_code']
            
            self.sku_to_name_mapping[sku_code] = product_name
            
            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)
        
        # Convert orders to use product names
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Item categories
        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()
        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))
        
        # Catalogue lookup
        for _, row in clean_catalogue.iterrows():
            product_name = row[self.product_name_column]
            self.catalogue_lookup[product_name] = {
                'name': product_name,
                'category_name': row['category_name'],
                'sku_codes': self.name_to_sku_mapping[product_name],
                'original_name': row.get('name', product_name),
                'parent_name': row.get('sku_parent_name', product_name)
            }
        
        # Enhanced feature computation
        self._build_product_cooccurrence(orders_with_names)
        self._build_category_affinity(orders_with_names)
        self._build_customer_archetypes(orders_with_names)
        self._build_customer_similarity(orders_with_names)
        self._build_reorder_patterns(orders_with_names)
        
        print(f"✅ Enhanced features computed successfully")
    
    def _build_product_cooccurrence(self, orders_with_names):
        """Build product co-occurrence matrix from EDA insights"""
        print(f"   🔗 Building product co-occurrence matrix...")
        
        # Build co-occurrence from order sessions
        for order_id, order_items in orders_with_names.groupby('display_order_id'):
            products = order_items['product_name'].tolist()
            
            # Calculate co-occurrence with confidence scores
            for i, product_a in enumerate(products):
                for j, product_b in enumerate(products):
                    if i != j:
                        self.product_cooccurrence[product_a][product_b] += 1
        
        # Normalize to confidence scores
        for product_a in self.product_cooccurrence:
            total_a = sum(self.product_cooccurrence[product_a].values())
            if total_a > 0:
                for product_b in self.product_cooccurrence[product_a]:
                    self.product_cooccurrence[product_a][product_b] /= total_a
        
        print(f"      ✅ Built co-occurrence for {len(self.product_cooccurrence)} products")
    
    def _build_category_affinity(self, orders_with_names):
        """Build category affinity patterns"""
        print(f"   📂 Building category affinity patterns...")
        
        # Merge with categories
        orders_with_categories = orders_with_names.merge(
            pd.DataFrame(list(self.item_categories.items()), columns=['product_name', 'category_name']),
            on='product_name', how='left'
        )
        
        # Build category co-occurrence
        for order_id, order_items in orders_with_categories.groupby('display_order_id'):
            categories = order_items['category_name'].dropna().tolist()
            
            for i, cat_a in enumerate(categories):
                for j, cat_b in enumerate(categories):
                    if i != j:
                        self.category_affinity[cat_a][cat_b] += 1
        
        # Normalize
        for cat_a in self.category_affinity:
            total_a = sum(self.category_affinity[cat_a].values())
            if total_a > 0:
                for cat_b in self.category_affinity[cat_a]:
                    self.category_affinity[cat_a][cat_b] /= total_a
        
        print(f"      ✅ Built affinity for {len(self.category_affinity)} categories")
    
    def _build_customer_archetypes(self, orders_with_names):
        """Build customer archetypes from EDA analysis"""
        print(f"   🎭 Building customer archetypes...")
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            unique_skus = customer_orders['product_name'].nunique()
            total_orders = len(customer_orders)
            unique_sessions = customer_orders['display_order_id'].nunique()
            avg_items_per_session = total_orders / unique_sessions if unique_sessions > 0 else 0
            
            # Determine archetype based on EDA findings
            if avg_items_per_session >= 13:
                archetype = 'bulk_buyer'  # Archetype 3
            elif avg_items_per_session <= 4:
                archetype = 'selective_buyer'  # Archetype 2
            elif unique_skus >= 80:
                archetype = 'diverse_explorer'  # Archetype 4
            elif unique_skus <= 65:
                archetype = 'focused_regular'  # Archetype 0
            else:
                archetype = 'balanced_shopper'  # Archetype 1
            
            # Calculate top product concentration
            product_frequencies = customer_orders['product_name'].value_counts()
            top_product_share = product_frequencies.iloc[0] / total_orders if len(product_frequencies) > 0 else 0
            
            self.customer_archetypes[customer_id] = {
                'archetype': archetype,
                'unique_skus': unique_skus,
                'avg_items_per_session': avg_items_per_session,
                'top_product_share': top_product_share,
                'total_orders': total_orders
            }
        
        print(f"      ✅ Classified {len(self.customer_archetypes)} customers into archetypes")
    
    def _build_customer_similarity(self, orders_with_names):
        """Build customer similarity matrix for collaborative filtering"""
        print(f"   👥 Building customer similarity matrix...")
        
        # Create customer-product matrix
        customer_product_matrix = orders_with_names.groupby(['customer_id', 'product_name']).size().unstack(fill_value=0)
        
        # Calculate cosine similarity (sample for efficiency)
        customers = customer_product_matrix.index.tolist()
        if len(customers) > 500:  # Sample for efficiency
            sample_customers = np.random.choice(customers, 500, replace=False)
            customer_product_matrix = customer_product_matrix.loc[sample_customers]
        
        # Compute similarity
        similarity_matrix = cosine_similarity(customer_product_matrix)
        
        # Store top similar customers for each customer
        for i, customer_id in enumerate(customer_product_matrix.index):
            similarities = similarity_matrix[i]
            top_similar_indices = np.argsort(similarities)[-11:-1]  # Top 10 similar (excluding self)
            
            similar_customers = []
            for idx in top_similar_indices:
                similar_customer = customer_product_matrix.index[idx]
                similarity_score = similarities[idx]
                if similarity_score > 0.1:  # Minimum similarity threshold
                    similar_customers.append((similar_customer, similarity_score))
            
            self.customer_similarity_matrix[customer_id] = similar_customers
        
        print(f"      ✅ Built similarity matrix for {len(self.customer_similarity_matrix)} customers")
    
    def _build_reorder_patterns(self, orders_with_names):
        """Build reorder patterns from EDA insights"""
        print(f"   🔄 Building reorder patterns...")
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            customer_patterns = {}
            
            for product_name, product_orders in customer_orders.groupby('product_name'):
                if len(product_orders) >= 2:
                    sorted_orders = product_orders.sort_values('delivery_date')
                    
                    intervals = []
                    for i in range(1, len(sorted_orders)):
                        interval = (sorted_orders.iloc[i]['delivery_date'] - 
                                  sorted_orders.iloc[i-1]['delivery_date']).days
                        intervals.append(interval)
                    
                    if intervals:
                        avg_interval = np.mean(intervals)
                        last_order_date = sorted_orders['delivery_date'].max()
                        
                        customer_patterns[product_name] = {
                            'avg_interval': avg_interval,
                            'last_order_date': last_order_date,
                            'order_count': len(sorted_orders),
                            'intervals': intervals
                        }
            
            if customer_patterns:
                self.reorder_patterns[customer_id] = customer_patterns
        
        print(f"      ✅ Built reorder patterns for {len(self.reorder_patterns)} customers")
    
    def calculate_dynamic_k_for_user(self, user_id, customer_orders_df):
        """Calculate dynamic k based on customer archetype and behavior"""
        if user_id in self.customer_archetypes:
            archetype_info = self.customer_archetypes[user_id]
            archetype = archetype_info['archetype']
            avg_items = archetype_info['avg_items_per_session']
            
            # Archetype-specific k calculation
            if archetype == 'bulk_buyer':
                return min(15, max(8, int(avg_items * 0.8)))
            elif archetype == 'selective_buyer':
                return min(8, max(3, int(avg_items * 1.2)))
            elif archetype == 'diverse_explorer':
                return min(12, max(6, int(avg_items * 0.9)))
            else:
                return min(10, max(5, int(avg_items)))
        
        # Fallback to median calculation
        user_orders = customer_orders_df[customer_orders_df['customer_id'] == user_id]
        if len(user_orders) == 0:
            return 8
        
        last_15_orders = user_orders.nlargest(15, 'delivery_date')
        items_per_order = last_15_orders.groupby('display_order_id').size()
        
        if len(items_per_order) == 0:
            return 8
        
        median_items = int(np.median(items_per_order))
        return max(3, min(15, median_items))
    
    def get_enhanced_recommendations(self, user_id, current_date, customer_orders_df, top_n=None):
        """
        Generate enhanced recommendations using ensemble approach
        
        Combines:
        1. Category-aware recommendations (99.8% repeat rate)
        2. Product co-occurrence patterns
        3. Collaborative filtering
        4. Reorder pattern predictions
        5. Archetype-specific strategies
        """
        if top_n is None:
            top_n = self.calculate_dynamic_k_for_user(user_id, customer_orders_df)
        
        # Get user's historical data
        user_orders = customer_orders_df[customer_orders_df['customer_id'] == user_id]
        if len(user_orders) == 0:
            return self._get_fallback_recommendations(top_n)
        
        # Convert to product names
        user_orders_with_names = user_orders.copy()
        user_orders_with_names['product_name'] = user_orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        user_orders_with_names = user_orders_with_names.dropna(subset=['product_name'])
        
        # Get user's purchase history
        user_products = set(user_orders_with_names['product_name'].unique())
        user_categories = set()
        for product in user_products:
            if product in self.item_categories:
                user_categories.add(self.item_categories[product])
        
        # Ensemble scoring
        all_recommendations = {}
        
        # 1. CATEGORY-AWARE RECOMMENDATIONS (Weight: 0.35)
        category_recs = self._get_category_aware_recommendations(user_id, user_categories, user_products)
        for product, score in category_recs.items():
            all_recommendations[product] = all_recommendations.get(product, 0) + score * 0.35
        
        # 2. CO-OCCURRENCE RECOMMENDATIONS (Weight: 0.25)
        cooccurrence_recs = self._get_cooccurrence_recommendations(user_products)
        for product, score in cooccurrence_recs.items():
            all_recommendations[product] = all_recommendations.get(product, 0) + score * 0.25
        
        # 3. COLLABORATIVE FILTERING (Weight: 0.20)
        collaborative_recs = self._get_collaborative_recommendations(user_id, user_products)
        for product, score in collaborative_recs.items():
            all_recommendations[product] = all_recommendations.get(product, 0) + score * 0.20
        
        # 4. REORDER PATTERN PREDICTIONS (Weight: 0.20)
        reorder_recs = self._get_reorder_recommendations(user_id, current_date)
        for product, score in reorder_recs.items():
            all_recommendations[product] = all_recommendations.get(product, 0) + score * 0.20
        
        # Remove already purchased items
        for product in user_products:
            if product in all_recommendations:
                del all_recommendations[product]
        
        # Sort and format recommendations
        sorted_recommendations = sorted(all_recommendations.items(), key=lambda x: x[1], reverse=True)
        
        final_recommendations = []
        for i, (product_name, score) in enumerate(sorted_recommendations[:top_n]):
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            final_recommendations.append({
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(score, 4),
                'predicted_quantity': 1.0,
                'recommendation_type': 'enhanced_ensemble'
            })
        
        return final_recommendations

    def _get_category_aware_recommendations(self, user_id, user_categories, user_products):
        """Get recommendations based on category patterns (99.8% repeat rate)"""
        recommendations = {}

        # Get user's archetype for category strategy
        archetype_info = self.customer_archetypes.get(user_id, {'archetype': 'balanced_shopper'})
        archetype = archetype_info['archetype']

        # Category-specific strategies
        for category in user_categories:
            category_weight = 1.0

            # Archetype-specific category weighting
            if archetype == 'bulk_buyer' and category == 'Vegetables':
                category_weight = 1.5  # Bulk buyers love vegetables
            elif archetype == 'diverse_explorer':
                category_weight = 0.8  # Diverse explorers spread across categories
            elif archetype == 'selective_buyer':
                category_weight = 1.2  # Selective buyers stick to preferred categories

            # Get products in this category
            category_products = [p for p, c in self.item_categories.items() if c == category]

            # Score products in category
            for product in category_products:
                if product not in user_products:  # Don't recommend already purchased
                    base_score = 0.8  # High base score due to 99.8% repeat rate

                    # Boost based on category affinity
                    affinity_boost = 0.0
                    for user_cat in user_categories:
                        if user_cat in self.category_affinity and category in self.category_affinity[user_cat]:
                            affinity_boost += self.category_affinity[user_cat][category]

                    final_score = (base_score + affinity_boost * 0.2) * category_weight
                    recommendations[product] = final_score

        return recommendations

    def _get_cooccurrence_recommendations(self, user_products):
        """Get recommendations based on product co-occurrence patterns"""
        recommendations = {}

        for user_product in user_products:
            if user_product in self.product_cooccurrence:
                for related_product, confidence in self.product_cooccurrence[user_product].items():
                    if related_product not in user_products and confidence > 0.01:  # Minimum confidence
                        # Boost score based on confidence
                        score = confidence * 2.0  # Amplify co-occurrence signal
                        recommendations[related_product] = recommendations.get(related_product, 0) + score

        return recommendations

    def _get_collaborative_recommendations(self, user_id, user_products):
        """Get recommendations based on similar customers"""
        recommendations = {}

        if user_id in self.customer_similarity_matrix:
            similar_customers = self.customer_similarity_matrix[user_id]

            for similar_customer, similarity_score in similar_customers:
                if similar_customer in self.customer_archetypes:
                    # Get similar customer's archetype for compatibility
                    similar_archetype = self.customer_archetypes[similar_customer]['archetype']
                    user_archetype = self.customer_archetypes.get(user_id, {'archetype': 'balanced_shopper'})['archetype']

                    # Archetype compatibility boost
                    compatibility_boost = 1.0
                    if similar_archetype == user_archetype:
                        compatibility_boost = 1.3

                    # This would require loading similar customer's products
                    # For now, use a simplified approach based on archetype patterns
                    archetype_products = self._get_archetype_typical_products(similar_archetype)

                    for product in archetype_products:
                        if product not in user_products:
                            score = similarity_score * compatibility_boost * 0.5
                            recommendations[product] = recommendations.get(product, 0) + score

        return recommendations

    def _get_reorder_recommendations(self, user_id, current_date):
        """Get recommendations based on reorder patterns"""
        recommendations = {}

        if user_id in self.reorder_patterns:
            user_patterns = self.reorder_patterns[user_id]

            for product, pattern_info in user_patterns.items():
                days_since_last = (current_date - pattern_info['last_order_date']).days
                avg_interval = pattern_info['avg_interval']
                order_count = pattern_info['order_count']

                # Calculate reorder probability
                if avg_interval > 0:
                    reorder_probability = min(1.0, days_since_last / avg_interval)

                    # Boost based on order frequency
                    frequency_boost = min(2.0, order_count / 5.0)

                    # Final reorder score
                    reorder_score = reorder_probability * frequency_boost * 0.8
                    recommendations[product] = reorder_score

        return recommendations

    def _get_archetype_typical_products(self, archetype):
        """Get typical products for an archetype (simplified)"""
        # This is a simplified version - in practice, you'd analyze actual archetype patterns
        archetype_patterns = {
            'bulk_buyer': ['Onion Nashik - Medium Size', 'Potato Regular', 'Tomato Hybrid', 'Carrot Orange'],
            'selective_buyer': ['Lemon', 'Coriander (Dhania)', 'Ginger New'],
            'diverse_explorer': ['Bell Pepper Red', 'Bell Pepper Yellow', 'Cucumber English', 'Spinach (Palak)'],
            'focused_regular': ['Banana Robusta', 'Apple Red Delicious', 'Milk Full Cream'],
            'balanced_shopper': ['Lemon', 'Tomato Hybrid', 'Onion Nashik - Medium Size']
        }

        return archetype_patterns.get(archetype, [])

    def _get_fallback_recommendations(self, top_n):
        """Get fallback recommendations for new users"""
        # Use most popular products from EDA insights
        popular_products = [
            'Lemon', 'Coriander (Dhania)', 'Ginger New', 'Cucumber English',
            'Bell Pepper Red', 'Bell Pepper Yellow', 'Onion Nashik - Medium Size',
            'Tomato Hybrid', 'Carrot Orange', 'Banana Robusta'
        ]

        fallback_recs = []
        for i, product_name in enumerate(popular_products[:top_n]):
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'

            fallback_recs.append({
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(0.5 - (i * 0.02), 4),
                'predicted_quantity': 1.0,
                'recommendation_type': 'fallback'
            })

        return fallback_recs

    def calculate_precision_recall_enhanced(self, customer_orders_df, test_start_date, test_end_date):
        """Calculate precision and recall for enhanced system"""
        print(f"📊 CALCULATING ENHANCED PRECISION AND RECALL")
        print(f"   📅 Test period: {test_start_date.strftime('%Y-%m-%d')} to {test_end_date.strftime('%Y-%m-%d')}")

        # Split data
        train_data = customer_orders_df[customer_orders_df['delivery_date'] < test_start_date]

        # Test data: orders placed during test period
        test_delivery_start = test_start_date + timedelta(days=1)
        test_delivery_end = test_end_date + timedelta(days=1)
        test_data = customer_orders_df[
            (customer_orders_df['delivery_date'] >= test_delivery_start) &
            (customer_orders_df['delivery_date'] <= test_delivery_end)
        ]

        print(f"   📊 Train orders: {len(train_data):,}")
        print(f"   📊 Test orders: {len(test_data):,}")

        # Precompute features on training data
        self.precompute_enhanced_features(train_data, pd.read_csv('catalogue_rc_with_parent_names.csv'))

        # Convert test data
        test_data_with_names = test_data.copy()
        test_data_with_names['product_name'] = test_data_with_names['sku_code'].map(self.sku_to_name_mapping)
        test_data_with_names = test_data_with_names.dropna(subset=['product_name'])
        test_data_with_names['order_date'] = test_data_with_names['delivery_date'] - timedelta(days=1)

        # Group actual orders by order_date
        actual_orders_by_date = {}
        for _, order in test_data_with_names.iterrows():
            order_date = order['order_date']
            user_id = order['customer_id']
            product_name = order['product_name']

            if order_date not in actual_orders_by_date:
                actual_orders_by_date[order_date] = {}

            if user_id not in actual_orders_by_date[order_date]:
                actual_orders_by_date[order_date][user_id] = set()

            actual_orders_by_date[order_date][user_id].add(product_name)

        # Calculate metrics
        precision_scores = []
        recall_scores = []
        f1_scores = []

        test_dates = [test_start_date + timedelta(days=i) for i in range((test_end_date - test_start_date).days + 1)]

        total_comparisons = 0
        for test_date in test_dates:
            if test_date not in actual_orders_by_date:
                continue

            users_with_orders = actual_orders_by_date[test_date]
            print(f"   📅 Testing {test_date.strftime('%Y-%m-%d')}: {len(users_with_orders)} users")

            for user_id, actual_items in users_with_orders.items():
                # Generate enhanced recommendations
                recommendations = self.get_enhanced_recommendations(user_id, test_date, train_data)
                recommended_items = set([rec['product_name'] for rec in recommendations])

                # Calculate metrics
                if len(recommended_items) > 0:
                    precision = len(recommended_items.intersection(actual_items)) / len(recommended_items)
                else:
                    precision = 0.0

                if len(actual_items) > 0:
                    recall = len(recommended_items.intersection(actual_items)) / len(actual_items)
                else:
                    recall = 0.0

                if precision + recall > 0:
                    f1 = 2 * (precision * recall) / (precision + recall)
                else:
                    f1 = 0.0

                precision_scores.append(precision)
                recall_scores.append(recall)
                f1_scores.append(f1)
                total_comparisons += 1

        # Calculate overall metrics
        avg_precision = np.mean(precision_scores) if precision_scores else 0.0
        avg_recall = np.mean(recall_scores) if recall_scores else 0.0
        avg_f1 = np.mean(f1_scores) if f1_scores else 0.0

        print(f"✅ ENHANCED PRECISION & RECALL RESULTS:")
        print(f"   📊 Average Precision: {avg_precision:.4f}")
        print(f"   📊 Average Recall: {avg_recall:.4f}")
        print(f"   📊 Average F1-Score: {avg_f1:.4f}")
        print(f"   👥 User-date combinations tested: {total_comparisons}")

        return {
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'avg_f1': avg_f1,
            'num_users_tested': total_comparisons,
            'precision_scores': precision_scores,
            'recall_scores': recall_scores,
            'f1_scores': f1_scores
        }
