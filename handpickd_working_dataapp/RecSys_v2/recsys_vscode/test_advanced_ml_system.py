#!/usr/bin/env python3
"""
🤖 TEST ADVANCED ML RECOMMENDATION SYSTEM

This script tests the cutting-edge ML recommendation system that combines:
1. Deep Learning for Sequence Prediction (LSTM/Transformer-based patterns)
2. Graph Neural Networks for Product Relationships (GCN/GraphSAGE-inspired)
3. Reinforcement Learning Optimization (Multi-Armed Bandit/DQN-inspired)
4. Advanced Neural Ensemble with ML filtering

TARGET: 50%+ precision and recall through state-of-the-art ML techniques
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from advanced_ml_recommendation_system import AdvancedMLRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the advanced ML recommendation system"""
    
    print("🤖 TESTING ADVANCED ML RECOMMENDATION SYSTEM")
    print("="*80)
    print("🎯 TARGET: 50%+ precision AND recall through advanced ML")
    print("🔬 TECHNIQUES:")
    print("   • Deep Learning for Sequence Prediction")
    print("   • Graph Neural Networks for Product Relationships")
    print("   • Reinforcement Learning Optimization")
    print("   • Advanced Neural Ensemble")
    print("="*80)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    print(f"✅ Date range: {customer_orders_rc['delivery_date'].min().strftime('%Y-%m-%d')} to {customer_orders_rc['delivery_date'].max().strftime('%Y-%m-%d')}")
    
    # Initialize advanced ML system
    print("\n🤖 INITIALIZING ADVANCED ML SYSTEM...")
    ml_system = AdvancedMLRecommendationSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING ADVANCED ML SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    ml_metrics = ml_system.calculate_precision_recall_advanced_ml(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Performance analysis
    print(f"\n📈 ADVANCED ML SYSTEM PERFORMANCE:")
    print(f"   🎯 Precision: {ml_metrics['avg_precision']:.4f} ({ml_metrics['avg_precision']*100:.1f}%)")
    print(f"   🎯 Recall: {ml_metrics['avg_recall']:.4f} ({ml_metrics['avg_recall']*100:.1f}%)")
    print(f"   🎯 F1-Score: {ml_metrics['avg_f1']:.4f} ({ml_metrics['avg_f1']*100:.1f}%)")
    
    # Compare with all previous systems
    baseline_precision = 0.22  # Original baseline
    enhanced_precision = 0.303  # Enhanced system
    ultra_precision = 0.305  # Ultra high precision
    
    print(f"\n📊 COMPREHENSIVE IMPROVEMENT ANALYSIS:")
    print(f"   📈 vs Baseline (~22%): {ml_metrics['avg_precision'] - baseline_precision:+.4f} ({(ml_metrics['avg_precision'] - baseline_precision)*100:+.1f}%)")
    print(f"   📈 vs Enhanced (30.3%): {ml_metrics['avg_precision'] - enhanced_precision:+.4f} ({(ml_metrics['avg_precision'] - enhanced_precision)*100:+.1f}%)")
    print(f"   📈 vs Ultra High (30.5%): {ml_metrics['avg_precision'] - ultra_precision:+.4f} ({(ml_metrics['avg_precision'] - ultra_precision)*100:+.1f}%)")
    
    # Target achievement analysis
    target_precision = 0.50
    target_recall = 0.50
    
    precision_achieved = ml_metrics['avg_precision'] >= target_precision
    recall_achieved = ml_metrics['avg_recall'] >= target_recall
    
    print(f"\n🎯 ADVANCED ML TARGET ACHIEVEMENT:")
    print(f"   Precision 50% target: {'✅ ACHIEVED' if precision_achieved else '❌ NOT ACHIEVED'} "
          f"({ml_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Recall 50% target: {'✅ ACHIEVED' if recall_achieved else '❌ NOT ACHIEVED'} "
          f"({ml_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    
    # Strategy performance analysis
    if 'strategy_metrics' in ml_metrics:
        print(f"\n🤖 ML STRATEGY PERFORMANCE:")
        strategy_metrics = ml_metrics['strategy_metrics']
        
        for strategy, metrics in strategy_metrics.items():
            print(f"   {strategy.replace('_', ' ').title()}: {metrics['precision']:.4f} precision ({metrics['count']} uses)")
            
            # Identify best performing strategy
            if metrics['precision'] >= 0.50:
                print(f"      ✅ 50%+ precision achieved with {strategy}!")
    
    # Reinforcement learning analysis
    if 'rl_rewards' in ml_metrics:
        print(f"\n🎮 REINFORCEMENT LEARNING ANALYSIS:")
        rl_rewards = ml_metrics['rl_rewards']
        
        best_strategy = None
        best_reward = 0
        
        for strategy, rewards in rl_rewards.items():
            if rewards:
                avg_reward = np.mean(rewards)
                print(f"   {strategy.replace('_', ' ').title()}: {avg_reward:.4f} avg reward ({len(rewards)} samples)")
                
                if avg_reward > best_reward:
                    best_reward = avg_reward
                    best_strategy = strategy
        
        if best_strategy:
            print(f"   🏆 Best RL Strategy: {best_strategy.replace('_', ' ').title()} ({best_reward:.4f} reward)")
            
            if best_reward >= 0.50:
                print(f"      ✅ RL discovered 50%+ precision strategy!")
    
    # Performance distribution analysis
    precision_scores = ml_metrics['precision_scores']
    recall_scores = ml_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"\n📊 ADVANCED ML PERFORMANCE DISTRIBUTION:")
        
        # Precision distribution
        high_precision_users = sum(1 for p in precision_scores if p >= 0.5)
        very_high_precision_users = sum(1 for p in precision_scores if p >= 0.7)
        medium_precision_users = sum(1 for p in precision_scores if 0.3 <= p < 0.5)
        
        print(f"   📈 Precision Distribution:")
        print(f"      Very High (≥70%): {very_high_precision_users:,} users ({very_high_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      High (≥50%): {high_precision_users:,} users ({high_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_precision_users:,} users ({medium_precision_users/len(precision_scores)*100:.1f}%)")
        
        # Both metrics analysis
        both_high_users = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.5 and r >= 0.5)
        both_very_high_users = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.7 and r >= 0.7)
        
        print(f"   🎯 Both precision AND recall:")
        print(f"      ≥70%: {both_very_high_users:,} users ({both_very_high_users/len(precision_scores)*100:.1f}%)")
        print(f"      ≥50%: {both_high_users:,} users ({both_high_users/len(precision_scores)*100:.1f}%)")
    
    # Sample advanced ML recommendations
    print(f"\n🔍 SAMPLE ADVANCED ML RECOMMENDATIONS:")
    print("-"*70)
    
    # Get test users
    test_users = customer_orders_rc['customer_id'].value_counts().head(5).index.tolist()
    
    for i, user_id in enumerate(test_users[:3]):
        print(f"\n👤 User {i+1}: {user_id[:8]}...")
        
        # Generate advanced ML recommendations
        recommendations = ml_system.get_advanced_ml_recommendations(user_id, max_date)
        
        if recommendations:
            selected_strategy = recommendations[0]['selected_strategy']
            print(f"   🤖 Selected Strategy: {selected_strategy.replace('_', ' ').title()}")
            
            print(f"   🚀 Advanced ML recommendations ({len(recommendations)} items):")
            for j, rec in enumerate(recommendations):
                print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, "
                      f"Confidence: {rec['ml_confidence']:.3f}, Cat: {rec['category']})")
        else:
            print(f"   ⚠️ No recommendations generated")
    
    # Advanced ML effectiveness analysis
    print(f"\n💡 ADVANCED ML EFFECTIVENESS ANALYSIS:")
    print("-"*70)
    
    if precision_achieved and recall_achieved:
        print(f"🎉 BREAKTHROUGH SUCCESS: 50%+ TARGET ACHIEVED WITH ADVANCED ML!")
        print(f"   ✅ Deep learning sequence prediction working")
        print(f"   ✅ Graph neural networks capturing relationships")
        print(f"   ✅ Reinforcement learning optimizing strategies")
        print(f"   ✅ Neural ensemble combining signals effectively")
        
    elif ml_metrics['avg_precision'] >= 0.45 or ml_metrics['avg_recall'] >= 0.45:
        print(f"📈 EXCELLENT PROGRESS: 45%+ accuracy with advanced ML!")
        print(f"   🤖 Advanced ML techniques showing strong impact")
        print(f"   🎯 Very close to 50% breakthrough target")
        print(f"   🔧 Fine-tune neural ensemble weights for final push")
        
    elif ml_metrics['avg_precision'] >= 0.35:
        print(f"📊 SIGNIFICANT IMPROVEMENT: 35%+ accuracy with ML!")
        print(f"   🤖 Advanced ML providing meaningful gains")
        print(f"   📈 Better than traditional approaches")
        print(f"   🔧 Optimize deep learning components")
        
    elif ml_metrics['avg_precision'] >= enhanced_precision:
        print(f"📈 MAINTAINED PERFORMANCE: ML matching enhanced system")
        print(f"   ✅ Advanced ML not degrading performance")
        print(f"   🤖 Complex ML techniques need optimization")
        print(f"   🔧 Simplify ensemble or improve training")
        
    else:
        print(f"🔧 ML OPTIMIZATION NEEDED")
        print(f"   🤖 Advanced ML techniques underperforming")
        print(f"   📊 Complex ensemble may be diluting signals")
        print(f"   🔍 Consider hybrid approach with simpler methods")
    
    # Business impact assessment
    print(f"\n💼 ADVANCED ML BUSINESS IMPACT:")
    print("-"*70)
    
    total_users_tested = ml_metrics['num_users_tested']
    if precision_scores:
        ml_effective_users = sum(1 for p in precision_scores if p >= 0.4)
        ml_business_impact = ml_effective_users / total_users_tested if total_users_tested > 0 else 0
        
        print(f"   📊 Users with 40%+ precision: {ml_effective_users:,}/{total_users_tested:,} ({ml_business_impact:.1%})")
        
        # ROI estimation for advanced ML
        if ml_metrics['avg_precision'] > enhanced_precision:
            ml_improvement_factor = ml_metrics['avg_precision'] / enhanced_precision
            print(f"   💰 ML improvement factor: {ml_improvement_factor:.2f}x over enhanced system")
            print(f"   🤖 Advanced ML ROI: {'Very High' if ml_improvement_factor > 1.5 else 'High' if ml_improvement_factor > 1.2 else 'Medium'}")
        
        # Deployment recommendation
        if precision_achieved and recall_achieved:
            print(f"   🚀 Recommendation: DEPLOY ADVANCED ML IMMEDIATELY - Breakthrough achieved!")
        elif ml_metrics['avg_precision'] >= 0.45:
            print(f"   📊 Recommendation: DEPLOY ML WITH MONITORING - Excellent performance")
        elif ml_metrics['avg_precision'] >= 0.35:
            print(f"   🔧 Recommendation: OPTIMIZE ML COMPONENTS - Good foundation")
        else:
            print(f"   🔍 Recommendation: HYBRID APPROACH - Combine ML with simpler methods")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"🎉 ADVANCED ML SYSTEM TEST COMPLETE!")
    
    if precision_achieved and recall_achieved:
        print(f"✅ BREAKTHROUGH: 50%+ TARGET ACHIEVED WITH ADVANCED ML!")
        print(f"🤖 ML Performance: Precision {ml_metrics['avg_precision']*100:.1f}% | Recall {ml_metrics['avg_recall']*100:.1f}%")
        print(f"🚀 READY FOR PRODUCTION - Advanced ML breakthrough!")
        
    elif ml_metrics['avg_precision'] >= 0.45 or ml_metrics['avg_recall'] >= 0.45:
        print(f"📈 EXCELLENT ML PROGRESS: 45%+ accuracy achieved!")
        print(f"🤖 ML Performance: Precision {ml_metrics['avg_precision']*100:.1f}% | Recall {ml_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Fine-tune for 50% breakthrough")
        
    elif ml_metrics['avg_precision'] >= 0.35:
        print(f"📊 GOOD ML IMPROVEMENT: 35%+ accuracy achieved!")
        print(f"🤖 ML Performance: Precision {ml_metrics['avg_precision']*100:.1f}% | Recall {ml_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Optimize ML components for higher accuracy")
        
    else:
        print(f"🔧 ML OPTIMIZATION NEEDED")
        print(f"📊 Current ML: Precision {ml_metrics['avg_precision']*100:.1f}% | Recall {ml_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*80)
    
    return ml_metrics

if __name__ == "__main__":
    results = main()
