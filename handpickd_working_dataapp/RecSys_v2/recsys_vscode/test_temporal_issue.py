import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from recsys import UltraOptimizedRecommendationSystem

# Load data
catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
customer_orders_rc = pd.read_csv('customer_orders.csv')
repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')

# Convert delivery_date to datetime
customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])

print("🔍 ANALYZING TEMPORAL RECOMMENDATION ISSUE")
print("="*60)

# Initialize system
system = UltraOptimizedRecommendationSystem(
    user_repurchase_ratios_df=repurchase_ratios_df,
    product_name_column='name'
)

system.precompute_static_features(customer_orders_rc, catalogue_rc)

# Get recent dates for testing
max_date = customer_orders_rc['delivery_date'].max()
test_dates = [max_date - timedelta(days=i) for i in range(6, -1, -1)]

print(f"📅 Testing dates: {[d.strftime('%Y-%m-%d') for d in test_dates]}")

# Test a few users across multiple days
test_users = customer_orders_rc['customer_id'].value_counts().head(3).index.tolist()
print(f"👥 Testing users: {test_users}")

# Generate recommendations for each user on each day
user_recommendations = {}

for test_date in test_dates:
    print(f"\n📅 Generating recommendations for {test_date.strftime('%Y-%m-%d')}")
    
    # Training data up to this date
    train_data = customer_orders_rc[customer_orders_rc['delivery_date'] < test_date]
    
    if len(train_data) == 0:
        continue
    
    # Build model
    model_data = system.build_matrices_ultra_fast(train_data)
    user_profiles = system.build_user_profiles_lightning_fast(
        model_data['user_item_matrix'], 
        model_data['user_item_qty_matrix']
    )
    
    # Generate recommendations for test users
    for user_id in test_users:
        if user_id in user_profiles:
            recommendations = system.get_recommendations_ultra_fast(
                user_id, user_profiles[user_id], model_data['similarity_dict'], top_n=5
            )
            
            if user_id not in user_recommendations:
                user_recommendations[user_id] = {}
            
            user_recommendations[user_id][test_date.strftime('%Y-%m-%d')] = [
                rec['product_name'] for rec in recommendations
            ]

# Analyze temporal patterns
print("\n🔍 TEMPORAL ANALYSIS RESULTS")
print("="*60)

for user_id in test_users:
    if user_id in user_recommendations:
        print(f"\n👤 User {user_id}:")
        user_recs = user_recommendations[user_id]
        
        # Check if recommendations are identical across days
        all_recs = list(user_recs.values())
        if len(all_recs) > 1:
            identical_days = 0
            for i in range(1, len(all_recs)):
                if all_recs[i] == all_recs[0]:
                    identical_days += 1
            
            print(f"   📊 Identical recommendation days: {identical_days}/{len(all_recs)-1}")
            
            # Show recommendations for each day
            for date, recs in user_recs.items():
                print(f"   📅 {date}: {recs}")
        else:
            print("   ⚠️ Insufficient data for temporal analysis")

print("\n🎯 ISSUE IDENTIFICATION:")
print("="*60)
print("If users show identical recommendations across multiple days,")
print("this indicates the system lacks temporal awareness and")
print("doesn't consider time-based purchasing patterns.")
