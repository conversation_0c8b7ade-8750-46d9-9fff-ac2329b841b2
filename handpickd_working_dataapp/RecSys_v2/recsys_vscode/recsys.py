# # -------------------------------------------------------------------------------- NOTEBOOK-CELL: CODE
# # Required imports

# from utils.notebookhelpers.helpers import Helpers
# from utils.dtos.templateOutputCollection import TemplateOutputCollection
# from utils.dtos.templateOutput import TemplateOutput
# from utils.dtos.templateOutput import OutputType
# from utils.dtos.templateOutput import ChartType
# from utils.dtos.variable import Metadata
# from utils.rcclient.commons.variable_datatype import VariableDatatype
# from utils.dtos.templateOutput import FileType
# from utils.dtos.rc_ml_model import RCMLModel
# from utils.notebookhelpers.helpers import Helpers
# from utils.libutils.vectorStores.utils import VectorStoreUtils

# context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())

# 
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import time
import hashlib
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import plotly.graph_objects as go
import plotly.express as px
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
# /Users/<USER>/Documents/rc/rc-customers/handpickd_working_dataapp/RecSys_v2/recsys_v4.py
# Load data contex

# from utils.notebookhelpers.helpers import Helpers
# context = Helpers.getOrCreateContext(contextId='contextId', localVars=locals())
# catalogue_rc = Helpers.getEntityData(context, 'catalogue_rc_with_parent_names')
# customer_orders_rc = Helpers.getEntityData(context, 'HighFrequencyCustomerOrders')
# repurchase_ratios_df = Helpers.getEntityData(context, 'repurchase_ratios')
catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
customer_orders_rc = pd.read_csv('customer_orders.csv')
repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')

class UltraOptimizedRecommendationSystem:
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        """
        Initialize the recommendation system with configurable product name column
        
        Args:
            user_repurchase_ratios_df: DataFrame with user repurchase ratios
            product_name_column: Column name to use for product identification ('name' or 'sku_parent_name')
        """
        # Configuration
        self.product_name_column = product_name_column
        print(f"🔧 Using '{product_name_column}' column for product identification")
        
        # Pre-computed static data (computed once) - now using configurable product names
        self.item_categories = {}
        self.item_purchase_frequency = {}
        self.item_avg_quantities = {}
        self.catalogue_lookup = {}
        self.name_to_sku_mapping = {}  # Map product names to available SKU codes
        self.sku_to_name_mapping = {}  # Map SKU codes to product names
        
        # Smart caching system
        self.cache = {}
        self.popular_items = []
        
        # User-specific repurchase ratios
        self.user_repurchase_ratios = {}
        self.default_repurchase_ratio = 0.7  # Fallback for users not in the dataset
        
        if user_repurchase_ratios_df is not None:
            self.load_user_repurchase_ratios(user_repurchase_ratios_df)
    
    def load_user_repurchase_ratios(self, repurchase_ratios_df):
        """Load user-specific repurchase ratios from DataFrame"""
        self.user_repurchase_ratios = dict(zip(
            repurchase_ratios_df['customer_id'], 
            repurchase_ratios_df['repurchase_ratio']
        ))
        print(f"✅ Loaded dynamic repurchase ratios for {len(self.user_repurchase_ratios):,} users")
        if len(self.user_repurchase_ratios) > 0:
            avg_ratio = repurchase_ratios_df['repurchase_ratio'].mean()
            print(f"   Average repurchase ratio: {avg_ratio:.3f}")
    
    def get_user_repurchase_ratio(self, user_id):
        """Get repurchase ratio for a specific user"""
        return self.user_repurchase_ratios.get(user_id, self.default_repurchase_ratio)
    
    def calculate_purchase_probability(self, recommendation, user_repurchase_ratio, user_id):
        """
        Calculate purchase probability based on recommendation context
        
        Args:
            recommendation: Dict containing recommendation details
            user_repurchase_ratio: User's repurchase ratio
            user_id: User ID
            
        Returns:
            float: Purchase probability between 0 and 1
        """
        product_name = recommendation['product_name']
        rec_score = recommendation['score']
        rec_type = recommendation['recommendation_type']
        
        # Get item's global popularity (now using configurable product names)
        item_popularity = self.item_purchase_frequency.get(product_name, 0.01)
        
        if rec_type == 'repurchase':
            # Higher base probability for repurchase items since user bought before
            # Base probability starts higher and is boosted by recommendation score
            base_prob = 0.6 + (0.3 * rec_score)  # Range: 0.6 to 0.9
            
            # Adjust by user's repurchase behavior
            purchase_probability = base_prob * user_repurchase_ratio
            
            # Small boost for popular items (items others frequently buy)
            popularity_boost = min(0.1, item_popularity * 2)  # Cap at 0.1
            purchase_probability += popularity_boost
            
        else:  # discovery items
            # Lower base probability for new items user hasn't purchased before
            # Base probability is more dependent on recommendation score
            base_prob = 0.2 + (0.4 * rec_score)  # Range: 0.2 to 0.6
            
            # Boost by item's global popularity (popular items more likely to be purchased)
            popularity_factor = 1 + (item_popularity * 3)  # Popularity has more impact for discovery
            purchase_probability = base_prob * popularity_factor
            
            # Slight adjustment based on user's openness to new items
            # Users with higher repurchase ratios might be less open to new items
            discovery_openness = 2 - user_repurchase_ratio  # Range: 1 to 1.3
            purchase_probability *= discovery_openness
        
        # Ensure probability is between 0 and 1
        purchase_probability = max(0.0, min(1.0, purchase_probability))
        
        return round(purchase_probability, 4)
    
    def filter_recommendations_personalized(self, recommendations_df):
        """
        Filter recommendations DataFrame using personalized thresholds based on user repurchase ratios
        
        Args:
            recommendations_df: DataFrame with recommendations containing 'customer_id', 'purchase_probability', 
                              and 'user_repurchase_ratio' columns
            
        Returns:
            DataFrame: Filtered recommendations that meet personalized thresholds
        """
        if recommendations_df.empty:
            return recommendations_df
        
        # Create a copy to avoid modifying the original
        filtered_df = recommendations_df.copy()
        
        # Add personalized threshold and user type columns
        def get_personalized_info(user_repurchase_ratio):
            if user_repurchase_ratio >= 0.8:
                return 0.6, 'Conservative'
            elif user_repurchase_ratio >= 0.5:
                return 0.45, 'Moderate'
            else:
                return 0.3, 'Exploratory'
        
        # Apply personalized logic
        filtered_df[['personalized_threshold', 'user_type']] = filtered_df['user_repurchase_ratio'].apply(
            lambda x: pd.Series(get_personalized_info(x))
        )
        
        # Filter based on personalized threshold
        filtered_df = filtered_df[filtered_df['purchase_probability'] >= filtered_df['personalized_threshold']]
        
        # Update show_recommendation to True for filtered items
        filtered_df['show_recommendation'] = True
        
        print(f"✅ Personalized filtering applied:")
        print(f"   Original recommendations: {len(recommendations_df):,}")
        print(f"   Filtered recommendations: {len(filtered_df):,}")
        print(f"   Filtering efficiency: {len(filtered_df)/len(recommendations_df)*100:.1f}%")
        
        # Show breakdown by user type
        user_type_counts = filtered_df['user_type'].value_counts()
        print(f"   User type breakdown:")
        for user_type, count in user_type_counts.items():
            avg_threshold = filtered_df[filtered_df['user_type'] == user_type]['personalized_threshold'].iloc[0]
            print(f"     {user_type}: {count:,} recommendations (threshold: {avg_threshold})")
        
        return filtered_df
    
    def get_selection_recommendations(self, recommendations_df, user_id, strategy='tiered'):
        """
        Provide selection guidance based on purchase probabilities
        
        Args:
            recommendations_df: DataFrame with recommendations
            user_id: User ID to filter recommendations
            strategy: Selection strategy ('tiered', 'type_based', 'top_n', 'personalized')
            
        Returns:
            dict: Selection recommendations with explanations
        """
        user_recs = recommendations_df[recommendations_df['customer_id'] == user_id].copy()
        
        if len(user_recs) == 0:
            return {'message': 'No recommendations found for this user'}
        
        user_repurchase_ratio = user_recs['user_repurchase_ratio'].iloc[0]
        
        if strategy == 'tiered':
            high_confidence = user_recs[user_recs['purchase_probability'] >= 0.7]
            medium_confidence = user_recs[(user_recs['purchase_probability'] >= 0.4) & 
                                        (user_recs['purchase_probability'] < 0.7)]
            low_confidence = user_recs[user_recs['purchase_probability'] < 0.4]
            
            return {
                'strategy': 'Tiered Confidence',
                'high_confidence': {
                    'count': len(high_confidence),
                    'items': high_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Strong buy - high likelihood of purchase'
                },
                'medium_confidence': {
                    'count': len(medium_confidence),
                    'items': medium_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Consider adding - moderate likelihood'
                },
                'low_confidence': {
                    'count': len(low_confidence),
                    'items': low_confidence[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'recommendation': 'Explore option - lower likelihood but might discover something new'
                }
            }
        
        elif strategy == 'type_based':
            repurchase_items = user_recs[user_recs['recommendation_type'] == 'repurchase']
            discovery_items = user_recs[user_recs['recommendation_type'] == 'discovery']
            
            # Different thresholds for different types
            strong_repurchase = repurchase_items[repurchase_items['purchase_probability'] >= 0.6]
            good_discovery = discovery_items[discovery_items['purchase_probability'] >= 0.35]
            
            return {
                'strategy': 'Type-Based Selection',
                'repurchase_recommendations': {
                    'count': len(strong_repurchase),
                    'items': strong_repurchase[['product_name', 'purchase_probability']].to_dict('records'),
                    'threshold': 0.6,
                    'explanation': 'Items you\'ve bought before with high repurchase probability'
                },
                'discovery_recommendations': {
                    'count': len(good_discovery),
                    'items': good_discovery[['product_name', 'purchase_probability']].to_dict('records'),
                    'threshold': 0.35,
                    'explanation': 'New items with good discovery potential'
                }
            }
        
        elif strategy == 'top_n':
            # Top 5-7 items with minimum threshold
            min_threshold = 0.3
            qualified_items = user_recs[user_recs['purchase_probability'] >= min_threshold]
            top_items = qualified_items.nlargest(7, 'purchase_probability')
            
            return {
                'strategy': 'Top-N with Minimum Threshold',
                'selected_items': {
                    'count': len(top_items),
                    'items': top_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'explanation': f'Top {len(top_items)} items with probability >= {min_threshold}'
                }
            }
        
        elif strategy == 'personalized':
            # Adjust thresholds based on user behavior
            if user_repurchase_ratio >= 0.8:  # Conservative user
                threshold = 0.6
                user_type = 'Conservative'
            elif user_repurchase_ratio >= 0.5:  # Moderate user
                threshold = 0.45
                user_type = 'Moderate'
            else:  # Exploratory user
                threshold = 0.3
                user_type = 'Exploratory'
            
            selected_items = user_recs[user_recs['purchase_probability'] >= threshold]
            
            return {
                'strategy': 'Personalized Threshold',
                'user_type': user_type,
                'user_repurchase_ratio': user_repurchase_ratio,
                'threshold': threshold,
                'selected_items': {
                    'count': len(selected_items),
                    'items': selected_items[['product_name', 'purchase_probability', 'recommendation_type']].to_dict('records'),
                    'explanation': f'Items selected based on your {user_type.lower()} shopping pattern'
                }
            }
        
        else:
            return {'error': 'Invalid strategy. Use: tiered, type_based, top_n, or personalized'}
        
    def precompute_static_features(self, customer_orders_df, catalogue_df):
        """Pre-compute all static features that don't change during evaluation - now using configurable product names"""
        print(f"🔧 Pre-computing static features using '{self.product_name_column}' column (one-time setup)...")
        
        # Validate that the specified column exists
        if self.product_name_column not in catalogue_df.columns:
            raise ValueError(f"Column '{self.product_name_column}' not found in catalogue. Available columns: {list(catalogue_df.columns)}")
        
        # Create clean catalogue with unique names (remove duplicates based on chosen column)
        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()
        
        # Create mappings between names and SKU codes
        print(f"📊 Creating {self.product_name_column}-to-SKU mappings...")
        for _, row in catalogue_df.iterrows():
            product_name = row[self.product_name_column]
            sku_code = row['sku_code']
            
            # Map SKU to name
            self.sku_to_name_mapping[sku_code] = product_name
            
            # Map name to list of SKU codes (multiple SKUs can have same name)
            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)
        
        # Convert customer orders to use product names
        print(f"📊 Converting orders to use {self.product_name_column}...")
        customer_orders_with_names = customer_orders_df.copy()
        customer_orders_with_names['product_name'] = customer_orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        
        # Remove any orders where we couldn't map the SKU to a name
        customer_orders_with_names = customer_orders_with_names.dropna(subset=['product_name'])
        
        # Item categories (using configurable column as keys)
        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))
        
        # Catalogue lookup (using configurable column as keys)
        self.catalogue_lookup = {}
        for _, row in clean_catalogue.iterrows():
            product_name = row[self.product_name_column]
            self.catalogue_lookup[product_name] = {
                'name': product_name,
                'category_name': row['category_name'],
                'sku_codes': self.name_to_sku_mapping[product_name],  # List of all SKU codes for this product
                'original_name': row.get('name', product_name),  # Keep original name for reference
                'parent_name': row.get('sku_parent_name', product_name)  # Keep parent name for reference
            }
        
        # Global item statistics (computed using product names)
        total_orders = customer_orders_with_names['display_order_id'].nunique()
        
        # Group by product name and aggregate
        name_counts = customer_orders_with_names.groupby('product_name').size()
        self.item_purchase_frequency = (name_counts / total_orders).to_dict()
        
        # Average quantities by product name
        self.item_avg_quantities = customer_orders_with_names.groupby('product_name')['ordered_qty'].mean().to_dict()
        
        # Popular items (products purchased by at least 3 users)
        name_user_counts = customer_orders_with_names.groupby('product_name')['customer_id'].nunique()
        self.popular_items = name_user_counts[name_user_counts >= 3].index.tolist()
        
        print(f"✅ Pre-computed {len(self.item_categories)} product categories using '{self.product_name_column}'")
        print(f"✅ Pre-computed {len(self.popular_items)} popular products")
        print(f"✅ Pre-computed mappings: {len(self.name_to_sku_mapping)} {self.product_name_column} values → {sum(len(skus) for skus in self.name_to_sku_mapping.values())} SKUs")
        print(f"✅ Pre-computed global statistics using {self.product_name_column}")
    
    def build_matrices_ultra_fast(self, orders_df):
        """Build matrices using ultra-fast vectorized operations - now using configurable product names"""
        
        # Convert orders to use product names
        orders_with_names = orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Create data hash for caching
        data_hash = hashlib.md5(f"{len(orders_with_names)}_{orders_with_names['delivery_date'].min()}_{orders_with_names['delivery_date'].max()}_{self.product_name_column}".encode()).hexdigest()
        
        if data_hash in self.cache:
            print(f"✅ Using cached matrices for '{self.product_name_column}'")
            return self.cache[data_hash]
        
        print(f"⚡ Building matrices (vectorized using {self.product_name_column})...")
        
        # User-item frequency matrix (using configurable product names)
        user_item_counts = orders_with_names.groupby(['customer_id', 'product_name']).size().reset_index(name='count')
        user_item_matrix = user_item_counts.pivot(index='customer_id', columns='product_name', values='count').fillna(0)
        
        # User-item quantity matrix (using configurable product names)
        user_item_qty = orders_with_names.groupby(['customer_id', 'product_name'])['ordered_qty'].mean().reset_index()
        user_item_qty_matrix = user_item_qty.pivot(index='customer_id', columns='product_name', values='ordered_qty').fillna(0)
        
        # 🚀 AGGRESSIVE SIMILARITY MATRIX for HIGH RECALL
        print(f"⚡ Computing AGGRESSIVE similarity matrix for high recall using {self.product_name_column}...")
        popular_in_matrix = [item for item in self.popular_items if item in user_item_matrix.columns]
        
        similarity_dict = {}
        if len(popular_in_matrix) > 0:
            # Use simplified Jaccard on binary matrix
            user_item_binary = (user_item_matrix[popular_in_matrix] > 0).astype(int)
            
            # MUCH MORE AGGRESSIVE similarity computation for high recall
            for i, item1 in enumerate(popular_in_matrix[:200]):  # Doubled from 100 to 200
                item1_users = user_item_binary[item1]
                similarities = {}
                
                for item2 in popular_in_matrix[:200]:  # Consider more items
                    if item1 != item2:
                        item2_users = user_item_binary[item2]
                        intersection = (item1_users & item2_users).sum()
                        union = (item1_users | item2_users).sum()
                        
                        if union > 0 and intersection >= 1:  # LOWERED from 2 to 1
                            jaccard_sim = intersection / union
                            if jaccard_sim > 0.05:  # LOWERED from 0.1 to 0.05 for more connections
                                similarities[item2] = jaccard_sim
                
                if similarities:
                    # Keep top 25 instead of 10 for more recommendations
                    top_similar = dict(sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:25])
                    similarity_dict[item1] = top_similar
        
        # Cache results
        result = {
            'user_item_matrix': user_item_matrix,
            'user_item_qty_matrix': user_item_qty_matrix,
            'similarity_dict': similarity_dict,
            'popular_items': popular_in_matrix
        }
        self.cache[data_hash] = result
        
        print(f"✅ Built matrices using {self.product_name_column}: {user_item_matrix.shape}, similarity: {len(similarity_dict)} items")
        return result
    
    def build_user_profiles_lightning_fast(self, user_item_matrix, user_item_qty_matrix):
        """Build user profiles with minimal computation - now using configurable product names"""
        
        print(f"⚡ Building user profiles (lightning fast using {self.product_name_column})...")
        user_profiles = {}
        
        for user_id in user_item_matrix.index:
            user_row = user_item_matrix.loc[user_id]
            user_items = user_row[user_row > 0].index.tolist()
            
            if not user_items:
                continue
            
            # Pre-computed data (no loops) - now using configurable product names
            purchase_counts = user_row[user_items].to_dict()
            
            # Simplified quantities
            quantities = {}
            if user_id in user_item_qty_matrix.index:
                qty_row = user_item_qty_matrix.loc[user_id]
                quantities = {item: qty for item, qty in qty_row[user_items].items() if qty > 0}
            
            # Simplified category preferences (using configurable product names)
            categories = defaultdict(int)
            for product_name in user_items:
                if product_name in self.item_categories:
                    categories[self.item_categories[product_name]] += purchase_counts[product_name]
            
            # Normalize
            total = sum(categories.values())
            if total > 0:
                categories = {cat: count/total for cat, count in categories.items()}
            
            user_profiles[user_id] = {
                'items': user_items,  # Now contains configurable product names
                'purchase_counts': purchase_counts,
                'quantities': quantities,
                'category_preferences': dict(categories),
                'purchased_items': user_items  # Now contains configurable product names
            }
        
        print(f"✅ Built {len(user_profiles)} user profiles using {self.product_name_column}")
        return user_profiles
    
    def get_recommendations_ultra_fast(self, user_id, user_profile, similarity_dict, top_n=10, repurchase_ratio=None):
        """Ultra-fast recommendation generation with minimal computation - now using configurable product names"""
        
        # Use user-specific repurchase ratio if not provided
        if repurchase_ratio is None:
            repurchase_ratio = self.get_user_repurchase_ratio(user_id)
        
        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names
        repurchase_count = max(1, int(top_n * repurchase_ratio))
        discovery_count = max(1, top_n - repurchase_count)
        
        # REPURCHASE RECOMMENDATIONS (simplified) - using configurable product names
        repurchase_scores = {}
        for product_name in purchased_items:
            count = user_profile['purchase_counts'].get(product_name, 0)
            # Simplified scoring for maximum speed
            repurchase_scores[product_name] = count
        
        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Normalize repurchase scores to 0-1 scale
        repurchase_recs = []
        if sorted_repurchase:
            max_repurchase_score = sorted_repurchase[0][1] if sorted_repurchase else 1
            min_repurchase_score = sorted_repurchase[-1][1] if len(sorted_repurchase) > 1 else 0
            score_range = max_repurchase_score - min_repurchase_score
            
            for product_name, raw_score in sorted_repurchase[:repurchase_count]:
                # Normalize score to 0-1 range
                if score_range > 0:
                    normalized_score = (raw_score - min_repurchase_score) / score_range
                else:
                    normalized_score = 1.0
                
                qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                
                # Select the first SKU code for this product name
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                repurchase_recs.append({
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'repurchase'
                })
        
        # DISCOVERY RECOMMENDATIONS (ultra-simplified) - using configurable product names
        discovery_scores = defaultdict(float)
        
        # Content-based (only for items with similarities)
        for product_name in purchased_items:
            if product_name in similarity_dict:
                count = user_profile['purchase_counts'].get(product_name, 0)
                for similar_item, sim in similarity_dict[product_name].items():
                    if similar_item not in purchased_items:
                        discovery_scores[similar_item] += sim * count
        
        # Popular items in user's preferred categories
        for product_name in self.popular_items[:200]:  # Only check top 200 popular items
            if product_name not in purchased_items and product_name in self.item_categories:
                category = self.item_categories[product_name]
                if category in user_profile['category_preferences']:
                    cat_pref = user_profile['category_preferences'][category]
                    popularity = self.item_purchase_frequency.get(product_name, 0)
                    discovery_scores[product_name] += cat_pref * popularity * 100  # Scale factor
        
        sorted_discovery = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Normalize discovery scores to 0-1 scale
        discovery_recs = []
        if sorted_discovery:
            max_discovery_score = sorted_discovery[0][1] if sorted_discovery else 1
            min_discovery_score = sorted_discovery[-1][1] if len(sorted_discovery) > 1 else 0
            score_range = max_discovery_score - min_discovery_score
            
            for product_name, raw_score in sorted_discovery[:discovery_count]:
                # Normalize score to 0-1 range
                if score_range > 0:
                    normalized_score = (raw_score - min_discovery_score) / score_range
                else:
                    normalized_score = 1.0
                
                qty = self.item_avg_quantities.get(product_name, 1.0)
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                
                # Select the first SKU code for this product name
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                discovery_recs.append({
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'discovery'
                })
        
        # Combine and return
        all_recs = repurchase_recs + discovery_recs
        all_recs.sort(key=lambda x: x['score'], reverse=True)
        return all_recs[:top_n]
    
    def get_recommendations_high_recall_ensemble(self, user_id, user_profile, similarity_dict, top_n=75, repurchase_ratio=None):
        """
        🚀 RADICAL HIGH-RECALL ENSEMBLE RECOMMENDATION SYSTEM
        
        Multi-strategy approach designed to achieve 80%+ recall:
        1. Comprehensive Repurchase (ALL items user bought)
        2. Aggressive Collaborative Filtering (relaxed thresholds)  
        3. Category Completion Strategy
        4. Popular Items Fallback
        5. Hierarchical Product Matching
        """
        
        if repurchase_ratio is None:
            repurchase_ratio = self.get_user_repurchase_ratio(user_id)
        
        purchased_items = set(user_profile['purchased_items'])  # Now contains configurable product names
        all_recommendations = {}  # Use dict to avoid duplicates
        
        # 🎯 STRATEGY 1: COMPREHENSIVE REPURCHASE (60% of recommendations - MORE AGGRESSIVE)
        repurchase_target = max(10, int(top_n * 0.6))  # Increased from 50% to 60%
        
        # Recommend ALL purchased items (not just top ones)
        repurchase_scores = {}
        for product_name in purchased_items:
            count = user_profile['purchase_counts'].get(product_name, 0)
            # Boost recent purchases and frequent purchases
            repurchase_scores[product_name] = count + 0.5  # Base boost
        
        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_repurchase[:repurchase_target]):
            if product_name in all_recommendations:
                continue
                
            # Higher scores for earlier items
            normalized_score = 0.9 - (i * 0.02)  # Start high, decrease slowly
            normalized_score = max(0.5, normalized_score)  # Minimum 0.5
            
            qty = user_profile['quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'repurchase',
                'strategy': 'comprehensive_repurchase'
            }
        
        # 🎯 STRATEGY 2: AGGRESSIVE COLLABORATIVE FILTERING (25% of recommendations)
        collab_target = max(5, int(top_n * 0.25))
        
        discovery_scores = defaultdict(float)
        
        # MUCH more aggressive similarity matching
        for product_name in purchased_items:
            if product_name in similarity_dict:
                count = user_profile['purchase_counts'].get(product_name, 0)
                # Include ALL similar items, not just top ones
                for similar_item, sim in similarity_dict[product_name].items():
                    if similar_item not in purchased_items and similar_item not in all_recommendations:
                        # Lower threshold and higher boost
                        discovery_scores[similar_item] += sim * count * 2  # Double the boost
        
        sorted_collab = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_collab[:collab_target]):
            if product_name in all_recommendations:
                continue
                
            normalized_score = 0.8 - (i * 0.03)  # High but decreasing
            normalized_score = max(0.4, normalized_score)
            
            qty = self.item_avg_quantities.get(product_name, 1.0)
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'discovery',
                'strategy': 'aggressive_collaborative'
            }
        
        # 🎯 STRATEGY 3: CATEGORY COMPLETION (15% of recommendations)
        category_target = max(3, int(top_n * 0.15))
        
        # For each category user shops in, recommend top items they haven't tried
        category_scores = defaultdict(float)
        for category, preference in user_profile['category_preferences'].items():
            if preference > 0.1:  # User shows interest in this category
                # Find popular items in this category they haven't bought
                for product_name in self.popular_items:
                    if (product_name not in purchased_items and 
                        product_name not in all_recommendations and
                        product_name in self.item_categories and
                        self.item_categories[product_name] == category):
                        
                        popularity = self.item_purchase_frequency.get(product_name, 0)
                        category_scores[product_name] += preference * popularity * 150  # Higher boost
        
        sorted_category = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (product_name, raw_score) in enumerate(sorted_category[:category_target]):
            if product_name in all_recommendations:
                continue
                
            normalized_score = 0.7 - (i * 0.04)
            normalized_score = max(0.3, normalized_score)
            
            qty = self.item_avg_quantities.get(product_name, 1.0)
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'discovery',
                'strategy': 'category_completion'
            }
        
        # 🎯 STRATEGY 4: POPULAR ITEMS FALLBACK (Remaining slots)
        remaining_slots = top_n - len(all_recommendations)
        if remaining_slots > 0:
            # Add globally popular items as safety net
            popular_scores = {}
            for product_name in self.popular_items[:150]:  # Increased from 100 to 150
                if (product_name not in purchased_items and 
                    product_name not in all_recommendations):
                    popularity = self.item_purchase_frequency.get(product_name, 0)
                    popular_scores[product_name] = popularity
            
            sorted_popular = sorted(popular_scores.items(), key=lambda x: x[1], reverse=True)
            
            for i, (product_name, raw_score) in enumerate(sorted_popular[:remaining_slots]):
                if product_name in all_recommendations:
                    continue
                    
                normalized_score = 0.5 - (i * 0.02)  # More generous scoring
                normalized_score = max(0.15, normalized_score)  # Lower minimum
                
                qty = self.item_avg_quantities.get(product_name, 1.0)
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                all_recommendations[product_name] = {
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(normalized_score, 4),
                    'predicted_quantity': round(qty, 3),
                    'recommendation_type': 'discovery',
                    'strategy': 'popular_fallback'
                }
        
        # Convert to list and sort by score
        final_recommendations = list(all_recommendations.values())
        final_recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return final_recommendations[:top_n]
    
    def calculate_personalized_k_values(self, customer_orders_df, lookback_purchases=15):
        """Calculate personalized k values based on median items in user's last N purchases"""
        
        print(f"🎯 Calculating personalized k values based on median items in last {lookback_purchases} purchases...")
        
        # Convert to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(customer_orders_df['delivery_date']):
            customer_orders_df = customer_orders_df.copy()
            customer_orders_df['delivery_date'] = pd.to_datetime(customer_orders_df['delivery_date'])
        
        personalized_k = {}
        
        # Group by customer and calculate median items per purchase
        for customer_id, customer_orders in customer_orders_df.groupby('customer_id'):
            # Sort by delivery date descending to get most recent purchases first
            customer_orders_sorted = customer_orders.sort_values('delivery_date', ascending=False)
            
            # Get unique orders (display_order_id) and their item counts
            order_item_counts = customer_orders_sorted.groupby('display_order_id').size()
            
            # Take the last N purchases
            recent_purchases = order_item_counts.head(lookback_purchases)
            
            if len(recent_purchases) > 0:
                # Calculate median number of items per purchase
                median_items = recent_purchases.median()
                
                # Set minimum k=3 and maximum k=50 for practical reasons
                personalized_k[customer_id] = max(3, min(50, int(median_items)))
            else:
                # Default fallback if no purchase history
                personalized_k[customer_id] = 10
        
        # Summary statistics
        k_values = list(personalized_k.values())
        print(f"✅ Calculated personalized k for {len(personalized_k):,} users")
        print(f"   Median k across all users: {np.median(k_values):.1f}")
        print(f"   Mean k across all users: {np.mean(k_values):.1f}")
        print(f"   Min k: {min(k_values)}, Max k: {max(k_values)}")
        
        # Distribution of k values
        k_distribution = pd.Series(k_values).value_counts().sort_index()
        print(f"   Most common k values: {k_distribution.head(5).to_dict()}")
        
        return personalized_k

    def evaluate_precision_recall_ultra_fast(self, customer_orders_df, k=None, test_days=7, use_personalized_k=True):
        """🚀 HIGH-RECALL EVALUATION using Multi-Strategy Ensemble with PERSONALIZED k values"""
        
        if use_personalized_k:
            print(f"🚀 HIGH-RECALL PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES")
            print(f"🎯 Using median items from last 15 purchases as personalized k for each user")
        else:
            print(f"🚀 HIGH-RECALL PRECISION & RECALL @ k={k} EVALUATION (USING {self.product_name_column.upper()})")
        print(f"🎯 TARGET: 80%+ RECALL with Multi-Strategy Ensemble Approach using '{self.product_name_column}' column")
        print("="*70)
        
        start_total = time.time()
        
        # Convert orders to use product names
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Calculate personalized k values if requested
        if use_personalized_k:
            personalized_k_values = self.calculate_personalized_k_values(customer_orders_df)
            default_k = int(np.median(list(personalized_k_values.values())))
            print(f"📊 Using personalized k values (median: {default_k})")
        else:
            personalized_k_values = {}
            default_k = k if k is not None else 10
            print(f"📊 Using fixed k={default_k} for all users")
        
        # Get evaluation dates
        max_date = orders_with_names['delivery_date'].max()
        evaluation_dates = [max_date - timedelta(days=i) for i in range(test_days-1, -1, -1)]
        today_date = pd.Timestamp(datetime.today().date())
        if today_date not in evaluation_dates:
            evaluation_dates.append(datetime.today().date())
        
        all_user_metrics = []
        daily_results = {}
        all_recommendations = []  # Store all recommendations for the new dataframe
        
        # Pre-compute test data for ALL dates at once (batch operation) - using configurable product names
        print(f"📊 Pre-computing test data for all dates using {self.product_name_column}...")
        test_data_all = {}
        for eval_date in evaluation_dates:
            test_data = orders_with_names[orders_with_names['delivery_date'] == eval_date]
            if len(test_data) > 0:
                # Group by customer and create sets of purchased product names
                test_grouped = test_data.groupby('customer_id')['product_name'].apply(set).to_dict()
                test_data_all[eval_date] = test_grouped
        
        for eval_date in evaluation_dates:
            if eval_date not in test_data_all:
                continue
                
            print(f"\n📅 {eval_date.strftime('%Y-%m-%d')}")
            day_start = time.time()
            
            # Training data (using original orders but will convert to names in build_matrices)
            train_data = customer_orders_df[customer_orders_df['delivery_date'] < eval_date]
            
            if len(train_data) == 0:
                continue
            
            print(f"   📊 Train: {len(train_data)} orders | Test: {len(test_data_all[eval_date])} users")
            
            # Build model (with caching) - now uses configurable product names internally
            model_data = self.build_matrices_ultra_fast(train_data)
            user_profiles = self.build_user_profiles_lightning_fast(
                model_data['user_item_matrix'], 
                model_data['user_item_qty_matrix']
            )
            
            # Generate recommendations using HIGH-RECALL ENSEMBLE (batch) with personalized k values
            print(f"   🚀 Generating HIGH-RECALL recommendations using Multi-Strategy Ensemble with {self.product_name_column}...")
            recommendations = {}
            user_k_values = {}  # Track k values used for each user
            
            for user_id in user_profiles:
                # Use personalized k value if available, otherwise use default
                user_k = personalized_k_values.get(user_id, default_k)
                user_k_values[user_id] = user_k
                
                recommendations[user_id] = self.get_recommendations_high_recall_ensemble(
                    user_id, user_profiles[user_id], model_data['similarity_dict'], user_k
                )
            
            # Collect all recommendations for the output dataframe with personalized filtering
            for user_id, user_recs in recommendations.items():
                user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)
                user_k = user_k_values.get(user_id, default_k)
                
                # 🚀 EXTREMELY PERMISSIVE thresholds for 80%+ RECALL (final push!)
                if user_repurchase_ratio >= 0.8:  # Conservative user
                    personalized_threshold = 0.15  # FURTHER LOWERED from 0.25 to 0.15
                    user_type = 'Conservative'
                elif user_repurchase_ratio >= 0.5:  # Moderate user
                    personalized_threshold = 0.10  # FURTHER LOWERED from 0.15 to 0.10
                    user_type = 'Moderate'
                else:  # Exploratory user
                    personalized_threshold = 0.05  # FURTHER LOWERED from 0.10 to 0.05
                    user_type = 'Exploratory'
                
                for rec in user_recs:
                    # Calculate purchase probability based on recommendation context
                    purchase_probability = self.calculate_purchase_probability(
                        rec, user_repurchase_ratio, user_id
                    )
                    
                    # Only include recommendations that meet the personalized threshold
                    if purchase_probability >= personalized_threshold:
                        recommendation_record = {
                            'sku_name': rec['product_name'],  # Keep backward compatibility
                            'product_name': rec['product_name'],  # New column for clarity
                            'sku_code': rec['sku_code'],
                            'show_recommendation': True,  # Set to True for filtered recommendations
                            'recommendation_type': rec['recommendation_type'],
                            'recommendation_score': rec['score'],
                            'purchase_probability': purchase_probability,  # New column with purchase confidence
                            'recommendation_date': eval_date,
                            'predicted_quantity': rec['predicted_quantity'],
                            'customer_id': user_id,
                            'category': rec['category'],
                            'user_repurchase_ratio': user_repurchase_ratio,  # Track the ratio used
                            'personalized_threshold': personalized_threshold,  # Track the threshold used
                            'user_type': user_type,  # Track user classification
                            'product_name_column_used': self.product_name_column,  # Track which column was used
                            'personalized_k': user_k  # Track the k value used for this user
                        }
                        all_recommendations.append(recommendation_record)
            
            # Evaluate (vectorized) - now using configurable product names for comparison
            print(f"   📊 Computing metrics using {self.product_name_column}...")
            actual_purchases = test_data_all[eval_date]
            
            metrics_list = []
            for user_id in recommendations:
                if user_id in actual_purchases:
                    # Get recommended product names (not SKU codes)
                    recommended_items = set([rec['product_name'] for rec in recommendations[user_id]])
                    actual_items = actual_purchases[user_id]  # Already contains configurable product names
                    user_k = user_k_values.get(user_id, default_k)
                    
                    hits = len(recommended_items.intersection(actual_items))
                    precision = hits / len(recommended_items) if len(recommended_items) > 0 else 0
                    recall = hits / len(actual_items) if len(actual_items) > 0 else 0
                    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                    hit_rate = 1 if hits > 0 else 0
                    
                    # Get user repurchase ratio for bucketing
                    user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)
                    
                    # Categorize user based on repurchase ratio
                    if user_repurchase_ratio >= 0.8:
                        user_bucket = 'repurchase'  # High repurchase users
                    elif user_repurchase_ratio >= 0.5:
                        user_bucket = 'balanced'    # Moderate repurchase users
                    else:
                        user_bucket = 'discovery'   # Low repurchase users (more exploratory)
                    
                    metrics_list.append({
                        'customer_id': user_id,  # Changed from user_id to customer_id for consistency
                        'user_id': user_id,      # Keep both for backward compatibility
                        'precision_at_k': precision,
                        'recall_at_k': recall,
                        'f1_at_k': f1,
                        'hit_rate': hit_rate,
                        'hits': hits,
                        'total_recommended': len(recommended_items),
                        'total_actual': len(actual_items),
                        'personalized_k': user_k,
                        'user_repurchase_ratio': user_repurchase_ratio,
                        'user_bucket': user_bucket
                    })
            
            # Daily averages (vectorized)
            if metrics_list:
                k_values_today = [m['personalized_k'] for m in metrics_list]
                daily_avg = {
                    'date': eval_date,
                    'users_evaluated': len(metrics_list),
                    'avg_precision_at_k': np.mean([m['precision_at_k'] for m in metrics_list]),
                    'avg_recall_at_k': np.mean([m['recall_at_k'] for m in metrics_list]),
                    'avg_f1_at_k': np.mean([m['f1_at_k'] for m in metrics_list]),
                    'avg_hit_rate': np.mean([m['hit_rate'] for m in metrics_list]),
                    'median_k': np.median(k_values_today),
                    'mean_k': np.mean(k_values_today),
                    'min_k': np.min(k_values_today),
                    'max_k': np.max(k_values_today),
                    'use_personalized_k': use_personalized_k
                }
                daily_results[eval_date] = daily_avg
                
                # Add to overall metrics
                for m in metrics_list:
                    m.update({'date': eval_date, 'use_personalized_k': use_personalized_k})
                    all_user_metrics.append(m)
                
                print(f"   👥 Users: {len(metrics_list)} | P@k: {daily_avg['avg_precision_at_k']:.3f} | R@k: {daily_avg['avg_recall_at_k']:.3f} | Med k: {daily_avg['median_k']:.0f}")
            
            day_time = time.time() - day_start
            print(f"   ⏱️ Day completed in {day_time:.1f}s")
        
        # Final results
        if all_user_metrics:
            all_k_values = [m['personalized_k'] for m in all_user_metrics]
            overall_metrics = {
                'use_personalized_k': use_personalized_k,
                'median_k': np.median(all_k_values),
                'mean_k': np.mean(all_k_values),
                'min_k': np.min(all_k_values),
                'max_k': np.max(all_k_values),
                'test_days': test_days,
                'total_evaluations': len(all_user_metrics),
                'total_days_evaluated': len(daily_results),
                'overall_avg_precision_at_k': np.mean([m['precision_at_k'] for m in all_user_metrics]),
                'overall_avg_recall_at_k': np.mean([m['recall_at_k'] for m in all_user_metrics]),
                'overall_avg_f1_at_k': np.mean([m['f1_at_k'] for m in all_user_metrics]),
                'overall_avg_hit_rate': np.mean([m['hit_rate'] for m in all_user_metrics]),
                'product_name_column_used': self.product_name_column
            }
        else:
            overall_metrics = {}
        
        total_time = time.time() - start_total
        
        print("\n" + "="*70)
        print(f"🏆 ULTRA-FAST EVALUATION COMPLETED IN {total_time:.1f} SECONDS!")
        print(f"🔍 NOW USING {self.product_name_column.upper()} FOR RECOMMENDATION MATCHING!")
        print("="*70)
        if overall_metrics:
            print(f"📊 Days evaluated: {overall_metrics['total_days_evaluated']}")
            print(f"📊 User evaluations: {overall_metrics['total_evaluations']:,}")
            print(f"📊 Product name column used: {overall_metrics['product_name_column_used']}")
            
            if use_personalized_k:
                print(f"📊 Personalized k values used: median={overall_metrics['median_k']:.0f}, mean={overall_metrics['mean_k']:.1f}, range={overall_metrics['min_k']:.0f}-{overall_metrics['max_k']:.0f}")
                print(f"\n🎯 FINAL HIGH-RECALL RESULTS @ PERSONALIZED k ({self.product_name_column.upper()} MATCHING):")
            else:
                print(f"\n🎯 FINAL HIGH-RECALL RESULTS @ k={default_k} ({self.product_name_column.upper()} MATCHING):")
            
            print(f"   Precision@k: {overall_metrics['overall_avg_precision_at_k']:.4f}")
            print(f"   Recall@k: {overall_metrics['overall_avg_recall_at_k']:.4f}")
            print(f"   F1@k: {overall_metrics['overall_avg_f1_at_k']:.4f}")
            print(f"   Hit Rate: {overall_metrics['overall_avg_hit_rate']:.4f}")
            
            # 🎯 RECALL TARGET CHECK
            if overall_metrics['overall_avg_recall_at_k'] >= 0.8:
                print(f"   🎉 SUCCESS! ACHIEVED 80%+ RECALL TARGET!")
            else:
                print(f"   ⚠️  Still below 80% target. Current: {overall_metrics['overall_avg_recall_at_k']*100:.1f}%")
        print("="*70)
        
        return {
            'overall_metrics': overall_metrics,
            'daily_results': daily_results,
            'all_user_metrics': all_user_metrics,
            'all_recommendations': all_recommendations,
            'total_time_seconds': total_time
        }

# def main():
print("🚀 ULTRA-OPTIMIZED PRECISION & RECALL EVALUATION WITH PERSONALIZED k VALUES")
print("="*70)
print("🎯 Maximum speed optimization with intelligent caching")
print("🔧 NEW: Configurable product name column (name vs sku_parent_name)")
print("🎯 NEW: Personalized k values based on user's last 15 purchase patterns")
print("="*70)

# Pre-compute all static features (one-time cost)
print("\n🔧 SETUP PHASE:")
setup_start = time.time()

# Convert delivery_date to datetime
customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])

# Configure which product name column to use
# Options: 'name' or 'sku_parent_name'
# Based on previous testing: 'sku_parent_name' generally gives higher recall
# PRODUCT_NAME_COLUMN = 'sku_parent_name'  # Change this to 'name' if needed
PRODUCT_NAME_COLUMN = 'name'

print(f"📊 Available columns in catalogue: {list(catalogue_rc.columns)}")
print(f"🔧 Using '{PRODUCT_NAME_COLUMN}' column for product identification")

# Validate that both columns exist (assuming they are always present)
if 'name' not in catalogue_rc.columns or 'sku_parent_name' not in catalogue_rc.columns:
    raise ValueError("Both 'name' and 'sku_parent_name' columns must be present in the catalogue")

# Show comparison stats
unique_names = catalogue_rc['name'].nunique()
unique_parent_names = catalogue_rc['sku_parent_name'].nunique()
print(f"📊 Unique 'name' values: {unique_names:,}")
print(f"📊 Unique 'sku_parent_name' values: {unique_parent_names:,}")

# Initialize system with chosen column
system = UltraOptimizedRecommendationSystem(
    user_repurchase_ratios_df=repurchase_ratios_df,
    product_name_column=PRODUCT_NAME_COLUMN
)

system.precompute_static_features(customer_orders_rc, catalogue_rc)

# Run evaluation with chosen column and personalized k values
results = system.evaluate_precision_recall_ultra_fast(customer_orders_rc, test_days=7, use_personalized_k=True)

setup_time = time.time() - setup_start
print(f"\n✅ Complete setup and evaluation completed in {setup_time:.2f} seconds")

# Save results
if results['overall_metrics']:
    print("\n💾 Saving results...")
    
    # Convert to DataFrames
    user_metrics_df = pd.DataFrame(results['all_user_metrics'])
    daily_metrics_df = pd.DataFrame([
        {**metrics, 'date': date.strftime('%Y-%m-%d')} 
        for date, metrics in results['daily_results'].items()
    ])
    recommendations_df = pd.DataFrame(results['all_recommendations'])
    
    # Analyze performance by user bucket
    if len(user_metrics_df) > 0 and 'user_bucket' in user_metrics_df.columns:
        print("\n📊 RECALL PERFORMANCE BY USER BUCKET:")
        print("="*60)
        
        bucket_analysis = user_metrics_df.groupby('user_bucket').agg({
            'recall_at_k': ['mean', 'median', 'std', 'count'],
            'precision_at_k': ['mean', 'median'],
            'f1_at_k': ['mean', 'median'],
            'hit_rate': 'mean',
            'personalized_k': 'mean',
            'user_repurchase_ratio': 'mean'
        }).round(4)
        
        # Flatten column names
        bucket_analysis.columns = ['_'.join(col).strip() for col in bucket_analysis.columns]
        
        print("User Bucket Performance Summary:")
        print("-" * 60)
        
        for bucket in ['repurchase', 'balanced', 'discovery']:
            if bucket in bucket_analysis.index:
                row = bucket_analysis.loc[bucket]
                print(f"\n{bucket.upper()} Users (repurchase ratio ≥ {0.8 if bucket=='repurchase' else 0.5 if bucket=='balanced' else 0.0}):")
                print(f"  Count: {int(row['recall_at_k_count'])} users")
                print(f"  Mean Recall@k: {row['recall_at_k_mean']:.4f} (±{row['recall_at_k_std']:.4f})")
                print(f"  Median Recall@k: {row['recall_at_k_median']:.4f}")
                print(f"  Mean Precision@k: {row['precision_at_k_mean']:.4f}")
                print(f"  Hit Rate: {row['hit_rate_mean']:.4f}")
                print(f"  Avg k value: {row['personalized_k_mean']:.1f}")
                print(f"  Avg repurchase ratio: {row['user_repurchase_ratio_mean']:.3f}")
        
        # Show best and worst performing bucket
        bucket_recall_means = bucket_analysis['recall_at_k_mean']
        if len(bucket_recall_means) > 1:
            best_bucket = bucket_recall_means.idxmax()
            worst_bucket = bucket_recall_means.idxmin()
            print(f"\n🏆 Best performing bucket: {best_bucket.upper()} (recall: {bucket_recall_means[best_bucket]:.4f})")
            print(f"⚠️  Worst performing bucket: {worst_bucket.upper()} (recall: {bucket_recall_means[worst_bucket]:.4f})")
        
        print("="*60)
    
    # Print filtering summary
    print("\n📊 PERSONALIZED FILTERING SUMMARY:")
    print("="*60)
    if len(recommendations_df) > 0:
        # Show overall statistics
        total_users = recommendations_df['customer_id'].nunique()
        total_recommendations = len(recommendations_df)
        product_column_used = recommendations_df['product_name_column_used'].iloc[0]
        
        print(f"Product name column used: {product_column_used}")
        print(f"Total users: {total_users:,}")
        print(f"Total personalized recommendations: {total_recommendations:,}")
        print(f"Average recommendations per user: {total_recommendations/total_users:.1f}")
        
        # Show k value distribution
        if 'personalized_k' in recommendations_df.columns:
            k_stats = recommendations_df['personalized_k'].describe()
            print(f"\nPersonalized k value distribution:")
            print(f"  Median k: {k_stats['50%']:.0f}")
            print(f"  Mean k: {k_stats['mean']:.1f}")
            print(f"  Range: {k_stats['min']:.0f} - {k_stats['max']:.0f}")
            
            # Show most common k values
            k_value_counts = recommendations_df['personalized_k'].value_counts().head(5)
            print(f"  Most common k values: {dict(k_value_counts)}")
        
        # Show breakdown by user type
        user_type_breakdown = recommendations_df['user_type'].value_counts()
        print(f"\nUser type breakdown:")
        for user_type, count in user_type_breakdown.items():
            threshold = recommendations_df[recommendations_df['user_type'] == user_type]['personalized_threshold'].iloc[0]
            print(f"  {user_type}: {count:,} recommendations (threshold ≥ {threshold})")
        
        # Show purchase probability distribution
        print(f"\nPurchase probability distribution:")
        print(f"  High confidence (≥0.6): {len(recommendations_df[recommendations_df['purchase_probability'] >= 0.6]):,}")
        print(f"  Medium confidence (0.4-0.59): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.4) & (recommendations_df['purchase_probability'] < 0.6)]):,}")
        print(f"  Low confidence (0.3-0.39): {len(recommendations_df[(recommendations_df['purchase_probability'] >= 0.3) & (recommendations_df['purchase_probability'] < 0.4)]):,}")
        
        # Show sample recommendations for each user type
        print(f"\n📋 Sample recommendations by user type:")
        for user_type in ['Conservative', 'Moderate', 'Exploratory']:
            if user_type in user_type_breakdown.index:
                sample_user_recs = recommendations_df[recommendations_df['user_type'] == user_type].head(3)
                print(f"\n  {user_type} user examples:")
                for _, rec in sample_user_recs.iterrows():
                    print(f"    • {rec['product_name']} (Prob: {rec['purchase_probability']:.3f}, Type: {rec['recommendation_type']})")
    
    print("\n💾 SAVED DATASETS:")
    print("="*60)
    print("1. user_metrics_df: Contains customer_id + recall/precision metrics by user bucket")
    print("2. recommendations_df: Contains customer_id + personalized recommendations")
    print("3. daily_metrics_df: Contains daily aggregated performance metrics")
    print("")
    print("💡 KEY BENEFITS:")
    print("✅ Customer IDs included for user bucket analysis")
    print("✅ User bucket categorization (repurchase/balanced/discovery)")
    print("✅ Personalized k values based on user purchase patterns")
    print("✅ Recommendations meet personalized thresholds - no additional filtering needed!")
    print("="*60)
    
    # Example usage
    print("\n🎯 USAGE EXAMPLE:")
    print("="*60)
    print("# How to use the configurable product name column and personalized k:")
    print("")
    print("# 1. Change the PRODUCT_NAME_COLUMN variable at the top:")
    print("# PRODUCT_NAME_COLUMN = 'name'  # or 'sku_parent_name'")
    print("")
    print("# 2. Choose evaluation mode:")
    print("# results = system.evaluate_precision_recall_ultra_fast(")
    print("#     customer_orders_rc, test_days=7, use_personalized_k=True)  # Dynamic k per user")
    print("# results = system.evaluate_precision_recall_ultra_fast(")
    print("#     customer_orders_rc, k=10, test_days=7, use_personalized_k=False)  # Fixed k=10")
    print("")
    print("# 3. Analyze results by user bucket:")
    print("# user_metrics_df = pd.DataFrame(results['all_user_metrics'])")
    print("# ")
    print("# # Filter by user bucket")
    print("# repurchase_users = user_metrics_df[user_metrics_df['user_bucket'] == 'repurchase']")
    print("# balanced_users = user_metrics_df[user_metrics_df['user_bucket'] == 'balanced']")
    print("# discovery_users = user_metrics_df[user_metrics_df['user_bucket'] == 'discovery']")
    print("# ")
    print("# # Compare recall by bucket")
    print("# bucket_recall = user_metrics_df.groupby('user_bucket')['recall_at_k'].mean()")
    print("# print(bucket_recall)")
    print("")
    print("# 4. The system will automatically use the chosen column for:")
    print("#    - Product identification and mapping")
    print("#    - Recommendation generation")
    print("#    - Recall calculation and evaluation")
    print("")
    print("# 5. User bucket categories:")
    print("#    - repurchase: High repurchase ratio (≥0.8)")
    print("#    - balanced: Moderate repurchase ratio (0.5-0.8)")
    print("#    - discovery: Low repurchase ratio (<0.5)")
    print("="*60)
    
    if len(recommendations_df) > 0:
        print("\n✅ SUCCESS! Your configurable recommendation system is ready!")
        print(f"   Product name column used: {recommendations_df['product_name_column_used'].iloc[0]}")
        print(f"   Total filtered recommendations: {len(recommendations_df):,}")
        print(f"   All items meet personalized purchase probability thresholds")
        print(f"   No additional filtering required!")
    else:
        print("\n⚠️  No recommendations met the personalized thresholds.")
        print("   Consider lowering thresholds or checking the input data.")
    
    # Create the two requested plots
    print("\n📊 CREATING VISUALIZATION PLOTS...")
    print("="*60)
    
    # Plot 1: Recall Distribution
    if len(user_metrics_df) > 0 and 'recall_at_k' in user_metrics_df.columns:
        # Create plotly histogram for recall distribution
        fig_1 = go.Figure()
        
        # Add histogram
        fig_1.add_trace(go.Histogram(
            x=user_metrics_df['recall_at_k'],
            nbinsx=30,
            opacity=0.7,
            marker_color='skyblue',
            marker_line_color='black',
            marker_line_width=1,
            name='Recall@10'
        ))
    
        # Calculate mean and median values
        mean_recall = user_metrics_df['recall_at_k'].mean()
        median_recall = user_metrics_df['recall_at_k'].median()
    
        # Add vertical line for mean with adjusted annotation position
        fig_1.add_vline(
            x=mean_recall,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Mean: {mean_recall:.3f}",
            annotation_position="top left",
            annotation_font=dict(size=10, color="red")
        )
    
        # Add vertical line for median with adjusted annotation position
        fig_1.add_vline(
            x=median_recall,
            line_dash="dash",
            line_color="orange",
            annotation_text=f"Median: {median_recall:.3f}",
            annotation_position="top right",
            annotation_font=dict(size=10, color="orange")
        )
    
        # Update layout with additional margin
        fig_1.update_layout(
            title='Recall@10 Distribution',
            xaxis_title='Recall@10',
            yaxis_title='Frequency',
            showlegend=False,
            template='plotly_white',
            margin=dict(l=40, r=40, t=60, b=40)
        )
    
        print(f"✅ Recall distribution plot created (n={len(user_metrics_df)} evaluations)")
    
    else:
        print("⚠️ No recall data available for plotting")
        fig_1 = go.Figure()  # Empty figure as fallback
    
    # Plot 2: Recommendation Score Distribution  
    if len(recommendations_df) > 0 and 'recommendation_score' in recommendations_df.columns:
        # Create plotly histogram for recommendation score distribution
        fig_2 = go.Figure()
        
        # Add histogram
        fig_2.add_trace(go.Histogram(
            x=recommendations_df['recommendation_score'],
            nbinsx=30,
            opacity=0.7,
            marker_color='lightgreen',
            marker_line_color='black',
            marker_line_width=1,
            name='Recommendation Score'
        ))
    
        # Calculate mean and median values
        mean_score = recommendations_df['recommendation_score'].mean()
        median_score = recommendations_df['recommendation_score'].median()
    
        # Add vertical line for mean with adjusted annotation position
        fig_2.add_vline(
            x=mean_score,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Mean: {mean_score:.3f}",
            annotation_position="top left",
            annotation_font=dict(size=10, color="red")
        )
    
        # Add vertical line for median with adjusted annotation position
        fig_2.add_vline(
            x=median_score,
            line_dash="dash",
            line_color="orange",
            annotation_text=f"Median: {median_score:.3f}",
            annotation_position="top right",
            annotation_font=dict(size=10, color="orange")
        )
    
        # Update layout with additional margin
        fig_2.update_layout(
            title='Recommendation Score Distribution',
            xaxis_title='Recommendation Score',
            yaxis_title='Frequency',
            showlegend=False,
            template='plotly_white',
            margin=dict(l=40, r=40, t=60, b=40)
        )
    
        print(f"✅ Recommendation score distribution plot created (n={len(recommendations_df)} recommendations)")
    
    else:
        print("⚠️ No recommendation score data available for plotting")
        fig_2 = go.Figure()  # Empty figure as fallback
    
    print("="*60)
    
    # Additional helper function to quickly test different columns
    def test_column_performance(column_name, description=""):
        """Helper function to quickly test different product name columns"""
        print(f"\n🧪 Testing '{column_name}' column {description}")
        print("-" * 50)
        
        try:
            test_system = UltraOptimizedRecommendationSystem(
                user_repurchase_ratios_df=repurchase_ratios_df,
                product_name_column=column_name
            )
            
            test_system.precompute_static_features(customer_orders_rc, catalogue_rc)
            
            # Quick test with 3 days
            test_results = test_system.evaluate_precision_recall_ultra_fast(customer_orders_rc, k=10, test_days=3)
            
            if test_results['overall_metrics']:
                recall = test_results['overall_metrics']['overall_avg_recall_at_k']
                precision = test_results['overall_metrics']['overall_avg_precision_at_k']
                print(f"✅ Quick test results - Recall: {recall:.4f}, Precision: {precision:.4f}")
                return recall, precision
            else:
                print("⚠️ No metrics available")
                return 0, 0
                
        except Exception as e:
            print(f"❌ Error testing '{column_name}': {str(e)}")
            return 0, 0
    
    print("\n🔬 QUICK COLUMN PERFORMANCE COMPARISON:")
    print("="*60)
    print("This helper function can be used to quickly test different product name columns")
    print("and compare their performance before running the full evaluation.")
    print("="*60)

else:
    print("⚠️ No overall metrics available for saving results")
    
print("\n" + "="*70)
print("🎉 CONFIGURABLE RECOMMENDATION SYSTEM EVALUATION COMPLETE!")
print("🔧 Key Features:")
print("   ✅ Configurable product name column ('name' or 'sku_parent_name')")
print("   ✅ Easy switching via PRODUCT_NAME_COLUMN variable")
print("   ✅ Personalized k values based on user's last 15 purchase patterns")
print("   ✅ Personalized filtering with dynamic thresholds")
print("   ✅ High-recall ensemble recommendation strategies")
print("   ✅ Comprehensive evaluation and visualization")
print("   ✅ Helper functions for quick column performance testing")
print("")
print("💡 TO TEST DIFFERENT CONFIGURATIONS:")
print("   - Change PRODUCT_NAME_COLUMN = 'name' or 'sku_parent_name'")
print("   - Toggle use_personalized_k=True/False in evaluation call")
print("   - Run the script to see performance with your chosen settings")
print("   - Use test_column_performance() for quick comparisons")
print("="*70)

# Helpers.save_output_dataset(context=context, output_name='last_one_week_recommendations-dynamick', data_frame=recommendations_df)
# Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recall-distribution-dynamick', plotly_fig=fig_1, group=None)
# Helpers.save_output_plotly_chart_as_json(context=context, chart_title='recommendation-score-distribution-dynamick', plotly_fig=fig_2, group=None)
# Helpers.save_output_dataset(context=context, output_name='user_metrics-dynamick', data_frame=user_metrics_df)