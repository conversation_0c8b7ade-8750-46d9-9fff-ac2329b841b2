#!/usr/bin/env python3
"""
🕒 TEMPORAL RECOMMENDATION SYSTEM SOLUTION DEMO

This script demonstrates the comprehensive solution to the temporal recommendation issue
where users were receiving identical recommendations across multiple days.

SOLUTION FEATURES:
1. ✅ Time-based user profiling with purchase frequency patterns
2. ✅ Recency-weighted scoring with temporal decay
3. ✅ Seasonality and availability awareness  
4. ✅ Dynamic recommendation diversification across time periods
5. ✅ Purchase cycle prediction and timing optimization

EXPECTED OUTCOME:
- Diverse, time-aware recommendations that reflect user purchasing patterns
- Significant improvement in recommendation diversity across time periods
- Better recall through temporal pattern recognition
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from temporal_recommendation_system import TemporalAwareRecommendationSystem
from temporal_evaluation_framework import TemporalEvaluationFramework

def main():
    print("🕒 TEMPORAL RECOMMENDATION SYSTEM SOLUTION")
    print("="*70)
    print("🎯 SOLVING: Static recommendations across time periods")
    print("🎯 GOAL: Dynamic, time-aware recommendations with improved diversity")
    print("="*70)
    
    # Load data
    print("\n📊 Loading data...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    
    # Initialize temporal recommendation system
    print("\n🕒 Initializing Temporal-Aware Recommendation System...")
    temporal_system = TemporalAwareRecommendationSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name',
        temporal_window_days=90,
        recency_decay_factor=0.1
    )
    
    # Pre-compute features
    temporal_system.precompute_static_features(customer_orders_rc, catalogue_rc)
    
    # Extract temporal patterns
    max_date = customer_orders_rc['delivery_date'].max()
    temporal_system.extract_temporal_patterns(customer_orders_rc, max_date)
    
    # Test the same users that showed static behavior in original system
    test_users = [
        'ec4592ed-ffa1-4528-82ca-006fd7994fc3',  # User A: Had some variation
        'a7ddccdf-ccb2-45b2-a87a-4bc6ccd46006',  # User B: 6/6 identical days
        'e442ffc5-99e5-4abd-89b7-b7bf9defc06d'   # User C: 6/6 identical days
    ]
    
    print(f"\n🧪 TESTING TEMPORAL SOLUTION ON PROBLEMATIC USERS")
    print("="*70)
    print("These users previously showed 6/6 identical recommendation days")
    
    # Generate temporal recommendations for each user across 7 days
    test_dates = [max_date - timedelta(days=i) for i in range(6, -1, -1)]
    
    for user_id in test_users:
        print(f"\n👤 User: {user_id}")
        print("-"*50)
        
        user_recommendations = {}
        
        # Generate recommendations for each day
        for test_date in test_dates:
            date_str = test_date.strftime('%Y-%m-%d')
            
            # Get temporal recommendations
            temporal_recs = temporal_system.get_temporal_recommendations(
                user_id, test_date, top_n=5, diversification_factor=0.3
            )
            
            user_recommendations[date_str] = [rec['product_name'] for rec in temporal_recs]
        
        # Analyze diversity
        all_recs = list(user_recommendations.values())
        all_items = set()
        for day_recs in all_recs:
            all_items.update(day_recs)
        
        # Calculate day-to-day similarity
        similarities = []
        dates = list(user_recommendations.keys())
        for i in range(1, len(dates)):
            prev_recs = set(user_recommendations[dates[i-1]])
            curr_recs = set(user_recommendations[dates[i]])
            
            intersection = len(prev_recs.intersection(curr_recs))
            union = len(prev_recs.union(curr_recs))
            similarity = intersection / union if union > 0 else 0
            similarities.append(similarity)
        
        avg_similarity = np.mean(similarities) if similarities else 0
        diversity_score = 1 - avg_similarity
        
        print(f"📊 TEMPORAL SOLUTION RESULTS:")
        print(f"   Total unique items across 7 days: {len(all_items)}")
        print(f"   Average day-to-day similarity: {avg_similarity:.3f}")
        print(f"   Diversity score: {diversity_score:.3f}")
        print(f"   Identical days: {sum(1 for s in similarities if s >= 0.99)}/{len(similarities)}")
        
        print(f"\n📅 Daily recommendations:")
        for date, recs in user_recommendations.items():
            print(f"   {date}: {recs}")
        
        # Get temporal insights
        insights = temporal_system.get_temporal_insights(user_id)
        if 'error' not in insights:
            print(f"\n💡 Temporal insights:")
            print(f"   Items due for repurchase: {len(insights['items_due_for_repurchase'])}")
            print(f"   Purchase cycles tracked: {insights['purchase_cycles_tracked']}")
            if insights['items_due_for_repurchase']:
                print(f"   Top due item: {insights['items_due_for_repurchase'][0]['item']} "
                      f"(probability: {insights['items_due_for_repurchase'][0]['probability']:.3f})")
    
    # Run comprehensive evaluation
    print(f"\n🔬 COMPREHENSIVE EVALUATION: ORIGINAL vs TEMPORAL SYSTEMS")
    print("="*70)
    
    evaluation_framework = TemporalEvaluationFramework(
        customer_orders_rc, catalogue_rc, repurchase_ratios_df
    )
    
    # Run evaluation on the problematic users
    results = evaluation_framework.run_comprehensive_evaluation(test_users=test_users)
    
    # Summary of improvements
    print(f"\n🎉 SOLUTION SUMMARY")
    print("="*70)
    
    overall = results['overall_metrics']
    if overall:
        print(f"✅ TEMPORAL AWARENESS IMPLEMENTED:")
        print(f"   - Time-based user profiling with purchase frequency patterns")
        print(f"   - Recency-weighted scoring with temporal decay")
        print(f"   - Seasonality and availability awareness")
        print(f"   - Dynamic recommendation diversification")
        print(f"   - Purchase cycle prediction and timing optimization")
        
        print(f"\n📈 QUANTIFIED IMPROVEMENTS:")
        print(f"   - Average diversity improvement: {overall['avg_diversity_improvement']:.4f}")
        print(f"   - Relative diversity improvement: {overall['relative_diversity_improvement']*100:.1f}%")
        print(f"   - Average unique items increase: {overall['avg_unique_items_improvement']:.1f}")
        
        print(f"\n🎯 PROBLEM RESOLUTION:")
        if overall['avg_temporal_diversity_score'] > overall['avg_original_diversity_score']:
            print(f"   ✅ SOLVED: Temporal system shows {overall['relative_diversity_improvement']*100:.1f}% better diversity")
            print(f"   ✅ Users now receive varied recommendations across time periods")
            print(f"   ✅ Recommendations reflect temporal purchasing patterns")
        else:
            print(f"   ⚠️ Partial improvement detected, may need further tuning")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"   1. Deploy temporal system to replace static recommendation logic")
    print(f"   2. Monitor recommendation diversity metrics in production")
    print(f"   3. Fine-tune temporal parameters based on user feedback")
    print(f"   4. Implement A/B testing to validate recall improvements")
    
    print(f"\n" + "="*70)
    print(f"🎉 TEMPORAL RECOMMENDATION SOLUTION COMPLETE!")
    print(f"🕒 Users will now receive dynamic, time-aware recommendations")
    print(f"📈 Expected outcome: Improved recall through temporal pattern recognition")
    print(f"="*70)

if __name__ == "__main__":
    main()
