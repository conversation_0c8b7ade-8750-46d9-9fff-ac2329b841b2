#!/usr/bin/env python3
"""
🎯 ULTRA HIGH PRECISION SYSTEM FOR 50%+ ACCURACY

Root Cause Analysis of Previous Failures:
1. Complex ensemble approaches dilute strong signals
2. Over-engineering reduces precision
3. Need to focus on PROVEN high-impact patterns only

NEW APPROACH - ULTRA FOCUSED:
1. ONLY use the strongest signals that actually work
2. High confidence thresholds to ensure quality
3. Conservative recommendation strategy
4. Focus on users with highest predictability
5. Leverage only the most reliable patterns

CORE STRATEGY:
- Start with enhanced temporal system (30.3% precision) as base
- Add ONLY proven enhancements that don't dilute signal
- Use very high confidence thresholds
- Target 50%+ through precision, not complexity
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from final_temporal_recommendation_system import FinalTemporalRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

class UltraHighPrecisionSystem(FinalTemporalRecommendationSystem):
    """Ultra high precision system targeting 50%+ accuracy through focused approach"""
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        # Initialize with enhanced temporal system as base
        super().__init__(user_repurchase_ratios_df, product_name_column)
        
        print(f"🎯 ULTRA HIGH PRECISION SYSTEM INITIALIZED")
        print(f"🚀 STRATEGY: Focus on proven patterns with high confidence")
        print(f"📊 BASE: Enhanced temporal system (30.3% precision)")
        print(f"🎯 TARGET: 50%+ precision through ultra-focused approach")
        
        # Ultra high precision features
        self.ultra_high_confidence_users = set()
        self.proven_reorder_patterns = {}
        self.high_confidence_categories = {}
        self.ultra_reliable_products = set()
        
    def build_ultra_high_precision_features(self, customer_orders_df, catalogue_df):
        """Build ultra high precision features focusing only on proven patterns"""
        print(f"🔧 BUILDING ULTRA HIGH PRECISION FEATURES...")

        # Store catalogue_df for use in methods
        self.catalogue_df = catalogue_df

        # First build base enhanced features
        self.precompute_static_features(customer_orders_df, catalogue_df)

        # Then add ultra high precision enhancements
        self._identify_ultra_high_confidence_users(customer_orders_df)
        self._build_proven_reorder_patterns(customer_orders_df)
        self._identify_high_confidence_categories(customer_orders_df)
        self._identify_ultra_reliable_products(customer_orders_df)

        print(f"✅ Ultra high precision features built")
    
    def _identify_ultra_high_confidence_users(self, customer_orders_df):
        """Identify users with ultra high predictability (top 10%)"""
        print(f"   🎯 Identifying ultra high confidence users...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        user_predictability = {}
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            total_orders = len(customer_orders)
            
            if total_orders >= 50:  # Minimum order threshold for high confidence
                # Product concentration
                product_frequencies = customer_orders['product_name'].value_counts()
                top_5_share = product_frequencies.head(5).sum() / total_orders
                
                # Temporal consistency (regular ordering)
                date_range = (customer_orders['delivery_date'].max() - customer_orders['delivery_date'].min()).days
                order_frequency = total_orders / max(1, date_range / 7)
                
                # Reorder consistency
                reorder_products = 0
                for product, product_orders in customer_orders.groupby('product_name'):
                    if len(product_orders) >= 3:  # Reordered at least 3 times
                        reorder_products += 1
                
                reorder_ratio = reorder_products / customer_orders['product_name'].nunique()
                
                # Ultra high confidence score
                confidence_score = (
                    top_5_share * 0.4 +  # Product concentration
                    min(1.0, order_frequency / 3) * 0.3 +  # Regular ordering
                    reorder_ratio * 0.3  # Reorder consistency
                )
                
                user_predictability[customer_id] = confidence_score
        
        # Select top 10% most predictable users
        if user_predictability:
            sorted_users = sorted(user_predictability.items(), key=lambda x: x[1], reverse=True)
            top_10_percent = int(len(sorted_users) * 0.1)
            
            for user_id, score in sorted_users[:top_10_percent]:
                if score > 0.7:  # Very high threshold
                    self.ultra_high_confidence_users.add(user_id)
        
        print(f"      ✅ Identified {len(self.ultra_high_confidence_users)} ultra high confidence users")
    
    def _build_proven_reorder_patterns(self, customer_orders_df):
        """Build only the most reliable reorder patterns"""
        print(f"   🔄 Building proven reorder patterns...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        for customer_id in self.ultra_high_confidence_users:
            customer_orders = orders_with_names[orders_with_names['customer_id'] == customer_id]
            user_patterns = {}
            
            for product_name, product_orders in customer_orders.groupby('product_name'):
                if len(product_orders) >= 5:  # High threshold for reliability
                    sorted_orders = product_orders.sort_values('delivery_date')
                    
                    # Calculate intervals
                    intervals = []
                    for i in range(1, len(sorted_orders)):
                        interval = (sorted_orders.iloc[i]['delivery_date'] - 
                                  sorted_orders.iloc[i-1]['delivery_date']).days
                        intervals.append(interval)
                    
                    if intervals:
                        # Only include if intervals are consistent (low variance)
                        avg_interval = np.mean(intervals)
                        std_interval = np.std(intervals)
                        consistency = 1 - (std_interval / avg_interval) if avg_interval > 0 else 0
                        
                        if consistency > 0.7:  # High consistency threshold
                            last_order_date = sorted_orders['delivery_date'].max()
                            
                            user_patterns[product_name] = {
                                'avg_interval': avg_interval,
                                'consistency': consistency,
                                'last_order_date': last_order_date,
                                'order_count': len(sorted_orders)
                            }
            
            if user_patterns:
                self.proven_reorder_patterns[customer_id] = user_patterns
        
        print(f"      ✅ Built proven patterns for {len(self.proven_reorder_patterns)} users")
    
    def _identify_high_confidence_categories(self, customer_orders_df):
        """Identify categories with highest repeat rates"""
        print(f"   📂 Identifying high confidence categories...")
        
        orders_with_categories = customer_orders_df.merge(
            self.catalogue_df[['sku_code', 'category_name']], 
            on='sku_code', 
            how='left'
        )
        
        category_repeat_rates = {}
        
        for category, category_orders in orders_with_categories.groupby('category_name'):
            if pd.isna(category):
                continue
                
            # Calculate repeat purchase rate
            users_with_repeats = 0
            total_users = category_orders['customer_id'].nunique()
            
            for customer_id, customer_category_orders in category_orders.groupby('customer_id'):
                if len(customer_category_orders) > 1:  # More than one purchase
                    users_with_repeats += 1
            
            repeat_rate = users_with_repeats / total_users if total_users > 0 else 0
            
            if repeat_rate > 0.8:  # 80%+ repeat rate
                category_repeat_rates[category] = repeat_rate
        
        self.high_confidence_categories = category_repeat_rates
        print(f"      ✅ Identified {len(self.high_confidence_categories)} high confidence categories")
    
    def _identify_ultra_reliable_products(self, customer_orders_df):
        """Identify products with highest reorder rates"""
        print(f"   🎯 Identifying ultra reliable products...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        product_reliability = {}
        
        for product, product_orders in orders_with_names.groupby('product_name'):
            # Calculate reorder rate
            users_with_reorders = 0
            total_users = product_orders['customer_id'].nunique()
            
            for customer_id, customer_product_orders in product_orders.groupby('customer_id'):
                if len(customer_product_orders) >= 3:  # Reordered at least 3 times
                    users_with_reorders += 1
            
            reorder_rate = users_with_reorders / total_users if total_users > 0 else 0
            
            if reorder_rate > 0.3 and total_users >= 10:  # High reorder rate + minimum users
                product_reliability[product] = reorder_rate
        
        # Select top 20% most reliable products
        if product_reliability:
            sorted_products = sorted(product_reliability.items(), key=lambda x: x[1], reverse=True)
            top_20_percent = int(len(sorted_products) * 0.2)
            
            for product, rate in sorted_products[:top_20_percent]:
                self.ultra_reliable_products.add(product)
        
        print(f"      ✅ Identified {len(self.ultra_reliable_products)} ultra reliable products")
    
    def get_ultra_high_precision_recommendations(self, user_id, current_date, top_n=8):
        """
        Generate ultra high precision recommendations
        
        ULTRA FOCUSED STRATEGY:
        1. Only recommend to ultra high confidence users with proven patterns
        2. Use very high confidence thresholds
        3. Focus on proven reorder patterns + high confidence categories
        4. Conservative approach to ensure 50%+ precision
        """
        
        # Only provide high-precision recommendations for ultra high confidence users
        if user_id not in self.ultra_high_confidence_users:
            # For other users, use conservative fallback
            return self._get_conservative_fallback(user_id, current_date, top_n)
        
        recommendations = {}
        
        # 1. PROVEN REORDER PATTERNS (70% weight)
        if user_id in self.proven_reorder_patterns:
            patterns = self.proven_reorder_patterns[user_id]
            
            for product, pattern in patterns.items():
                days_since_last = (current_date - pattern['last_order_date']).days
                avg_interval = pattern['avg_interval']
                consistency = pattern['consistency']
                
                # High confidence reorder prediction
                if avg_interval > 0:
                    reorder_probability = min(1.0, days_since_last / avg_interval)
                    
                    # Only recommend if high probability and high consistency
                    if reorder_probability > 0.7 and consistency > 0.8:
                        confidence_score = reorder_probability * consistency * 0.7
                        recommendations[product] = confidence_score
        
        # 2. HIGH CONFIDENCE CATEGORY RECOMMENDATIONS (30% weight)
        if user_id in self.customer_category_loyalty:
            user_categories = self.customer_category_loyalty[user_id]
            
            for category, loyalty_score in user_categories.items():
                if category in self.high_confidence_categories and loyalty_score > 0.3:
                    category_confidence = self.high_confidence_categories[category]
                    
                    # Get ultra reliable products in this category
                    category_products = [p for p in self.ultra_reliable_products 
                                       if self.item_categories.get(p) == category]
                    
                    for product in category_products:
                        if product not in recommendations:  # Don't override reorder patterns
                            category_score = loyalty_score * category_confidence * 0.3
                            recommendations[product] = category_score
        
        # Filter by minimum confidence threshold
        high_confidence_recs = {
            product: score for product, score in recommendations.items() 
            if score > 0.5  # Very high confidence threshold
        }
        
        # Sort and format
        sorted_recs = sorted(high_confidence_recs.items(), key=lambda x: x[1], reverse=True)
        
        final_recommendations = []
        for i, (product_name, score) in enumerate(sorted_recs[:top_n]):
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
            
            final_recommendations.append({
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(score, 4),
                'predicted_quantity': 1.0,
                'recommendation_type': 'ultra_high_precision',
                'confidence_level': 'ultra_high'
            })
        
        return final_recommendations
    
    def _get_conservative_fallback(self, user_id, current_date, top_n):
        """Conservative fallback for non-ultra-high-confidence users"""
        # Use enhanced temporal system for other users
        return self.get_temporal_recommendations(user_id, current_date, min(top_n, 5))
    
    def calculate_dynamic_k_for_user(self, user_id, customer_orders_df):
        """Conservative dynamic k for ultra high precision"""
        if user_id in self.ultra_high_confidence_users:
            # Fewer recommendations for higher precision
            return 6
        else:
            # Use base system for others
            return super().calculate_dynamic_k_for_user(user_id, customer_orders_df)

    def calculate_precision_recall_ultra_high(self, customer_orders_df, test_start_date, test_end_date):
        """Calculate precision and recall for ultra high precision system"""
        print(f"📊 CALCULATING ULTRA HIGH PRECISION & RECALL")
        print(f"   📅 Test period: {test_start_date.strftime('%Y-%m-%d')} to {test_end_date.strftime('%Y-%m-%d')}")
        print(f"   🎯 Using ultra high precision targeting (top 10% users)")

        # Split data
        train_data = customer_orders_df[customer_orders_df['delivery_date'] < test_start_date]

        # Test data: orders placed during test period
        test_delivery_start = test_start_date + timedelta(days=1)
        test_delivery_end = test_end_date + timedelta(days=1)
        test_data = customer_orders_df[
            (customer_orders_df['delivery_date'] >= test_delivery_start) &
            (customer_orders_df['delivery_date'] <= test_delivery_end)
        ]

        print(f"   📊 Train orders: {len(train_data):,}")
        print(f"   📊 Test orders: {len(test_data):,}")

        # Build ultra high precision features on training data
        self.build_ultra_high_precision_features(train_data, pd.read_csv('catalogue_rc_with_parent_names.csv'))

        # Extract temporal patterns for base system
        self.extract_temporal_patterns(train_data, test_start_date)

        # Convert test data
        test_data_with_names = test_data.copy()
        test_data_with_names['product_name'] = test_data_with_names['sku_code'].map(self.sku_to_name_mapping)
        test_data_with_names = test_data_with_names.dropna(subset=['product_name'])
        test_data_with_names['order_date'] = test_data_with_names['delivery_date'] - timedelta(days=1)

        # Group actual orders by order_date
        actual_orders_by_date = {}
        for _, order in test_data_with_names.iterrows():
            order_date = order['order_date']
            user_id = order['customer_id']
            product_name = order['product_name']

            if order_date not in actual_orders_by_date:
                actual_orders_by_date[order_date] = {}

            if user_id not in actual_orders_by_date[order_date]:
                actual_orders_by_date[order_date][user_id] = set()

            actual_orders_by_date[order_date][user_id].add(product_name)

        # Calculate metrics
        precision_scores = []
        recall_scores = []
        f1_scores = []

        ultra_high_precision_scores = []
        ultra_high_recall_scores = []
        regular_precision_scores = []
        regular_recall_scores = []

        test_dates = [test_start_date + timedelta(days=i) for i in range((test_end_date - test_start_date).days + 1)]

        total_comparisons = 0
        ultra_high_users_tested = 0

        for test_date in test_dates:
            if test_date not in actual_orders_by_date:
                continue

            users_with_orders = actual_orders_by_date[test_date]
            print(f"   📅 Testing {test_date.strftime('%Y-%m-%d')}: {len(users_with_orders)} users")

            for user_id, actual_items in users_with_orders.items():
                # Generate ultra high precision recommendations
                dynamic_k = self.calculate_dynamic_k_for_user(user_id, train_data)
                recommendations = self.get_ultra_high_precision_recommendations(user_id, test_date, dynamic_k)
                recommended_items = set([rec['product_name'] for rec in recommendations])

                # Calculate metrics
                if len(recommended_items) > 0:
                    precision = len(recommended_items.intersection(actual_items)) / len(recommended_items)
                else:
                    precision = 0.0

                if len(actual_items) > 0:
                    recall = len(recommended_items.intersection(actual_items)) / len(actual_items)
                else:
                    recall = 0.0

                if precision + recall > 0:
                    f1 = 2 * (precision * recall) / (precision + recall)
                else:
                    f1 = 0.0

                precision_scores.append(precision)
                recall_scores.append(recall)
                f1_scores.append(f1)

                # Track by user type
                if user_id in self.ultra_high_confidence_users:
                    ultra_high_precision_scores.append(precision)
                    ultra_high_recall_scores.append(recall)
                    ultra_high_users_tested += 1
                else:
                    regular_precision_scores.append(precision)
                    regular_recall_scores.append(recall)

                total_comparisons += 1

        # Calculate overall metrics
        avg_precision = np.mean(precision_scores) if precision_scores else 0.0
        avg_recall = np.mean(recall_scores) if recall_scores else 0.0
        avg_f1 = np.mean(f1_scores) if f1_scores else 0.0

        # Calculate metrics by user type
        ultra_high_precision = np.mean(ultra_high_precision_scores) if ultra_high_precision_scores else 0.0
        ultra_high_recall = np.mean(ultra_high_recall_scores) if ultra_high_recall_scores else 0.0
        regular_precision = np.mean(regular_precision_scores) if regular_precision_scores else 0.0
        regular_recall = np.mean(regular_recall_scores) if regular_recall_scores else 0.0

        print(f"✅ ULTRA HIGH PRECISION & RECALL RESULTS:")
        print(f"   📊 Overall Average Precision: {avg_precision:.4f}")
        print(f"   📊 Overall Average Recall: {avg_recall:.4f}")
        print(f"   📊 Overall Average F1-Score: {avg_f1:.4f}")
        print(f"   👥 Total user-date combinations tested: {total_comparisons}")

        print(f"\n📊 RESULTS BY USER TYPE:")
        print(f"   🎯 Ultra High Confidence Users ({ultra_high_users_tested} tested):")
        print(f"      Precision: {ultra_high_precision:.4f}, Recall: {ultra_high_recall:.4f}")
        print(f"   👥 Regular Users ({total_comparisons - ultra_high_users_tested} tested):")
        print(f"      Precision: {regular_precision:.4f}, Recall: {regular_recall:.4f}")

        print(f"   🚀 Ultra high precision targeting: {len(self.ultra_high_confidence_users)} users identified")

        return {
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'avg_f1': avg_f1,
            'num_users_tested': total_comparisons,
            'precision_scores': precision_scores,
            'recall_scores': recall_scores,
            'f1_scores': f1_scores,
            'ultra_high_precision': ultra_high_precision,
            'ultra_high_recall': ultra_high_recall,
            'ultra_high_users_tested': ultra_high_users_tested,
            'regular_precision': regular_precision,
            'regular_recall': regular_recall,
            'enhancement_type': 'ultra_high_precision'
        }
