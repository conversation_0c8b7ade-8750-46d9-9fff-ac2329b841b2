import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from collections import defaultdict
from temporal_recommendation_system import TemporalAwareRecommendationSystem
from recsys import UltraOptimizedRecommendationSystem

class TemporalEvaluationFramework:
    """
    🕒 TEMPORAL EVALUATION FRAMEWORK
    
    Comprehensive testing framework to validate temporal recommendation improvements:
    1. Compare original vs temporal-aware systems
    2. Measure recommendation diversity across time periods
    3. Analyze temporal pattern effectiveness
    4. Validate recall improvements with temporal logic
    """
    
    def __init__(self, customer_orders_df, catalogue_df, repurchase_ratios_df):
        """Initialize evaluation framework with data"""
        self.customer_orders_df = customer_orders_df.copy()
        self.catalogue_df = catalogue_df.copy()
        self.repurchase_ratios_df = repurchase_ratios_df.copy()
        
        # Convert delivery_date to datetime
        self.customer_orders_df['delivery_date'] = pd.to_datetime(self.customer_orders_df['delivery_date'])
        
        print("🕒 TEMPORAL EVALUATION FRAMEWORK INITIALIZED")
        print(f"   📊 Orders: {len(self.customer_orders_df):,}")
        print(f"   📊 Products: {len(self.catalogue_df):,}")
        print(f"   📊 Users: {len(self.repurchase_ratios_df):,}")
    
    def compare_systems(self, test_users=None, test_days=7, top_n=5):
        """
        Compare original vs temporal-aware recommendation systems
        
        Args:
            test_users: List of user IDs to test (None for top users)
            test_days: Number of days to test
            top_n: Number of recommendations per day
            
        Returns:
            dict: Comparison results
        """
        print("\n🔄 COMPARING ORIGINAL VS TEMPORAL-AWARE SYSTEMS")
        print("="*70)
        
        # Initialize both systems
        print("🔧 Initializing Original System...")
        original_system = UltraOptimizedRecommendationSystem(
            user_repurchase_ratios_df=self.repurchase_ratios_df,
            product_name_column='name'
        )
        original_system.precompute_static_features(self.customer_orders_df, self.catalogue_df)
        
        print("🕒 Initializing Temporal-Aware System...")
        temporal_system = TemporalAwareRecommendationSystem(
            user_repurchase_ratios_df=self.repurchase_ratios_df,
            product_name_column='name'
        )
        temporal_system.precompute_static_features(self.customer_orders_df, self.catalogue_df)
        
        # Extract temporal patterns
        max_date = self.customer_orders_df['delivery_date'].max()
        temporal_system.extract_temporal_patterns(self.customer_orders_df, max_date)
        
        # Select test users
        if test_users is None:
            test_users = self.customer_orders_df['customer_id'].value_counts().head(5).index.tolist()
        
        print(f"👥 Testing {len(test_users)} users over {test_days} days")
        
        # Generate test dates
        test_dates = [max_date - timedelta(days=i) for i in range(test_days-1, -1, -1)]
        
        comparison_results = {
            'original_recommendations': {},
            'temporal_recommendations': {},
            'diversity_metrics': {},
            'user_insights': {}
        }
        
        for user_id in test_users:
            print(f"\n👤 Testing User: {user_id}")
            
            original_recs_by_date = {}
            temporal_recs_by_date = {}
            
            # Generate recommendations for each test date
            for test_date in test_dates:
                date_str = test_date.strftime('%Y-%m-%d')
                
                # Original system recommendations
                train_data = self.customer_orders_df[self.customer_orders_df['delivery_date'] < test_date]
                if len(train_data) > 0:
                    model_data = original_system.build_matrices_ultra_fast(train_data)
                    user_profiles = original_system.build_user_profiles_lightning_fast(
                        model_data['user_item_matrix'], 
                        model_data['user_item_qty_matrix']
                    )
                    
                    if user_id in user_profiles:
                        original_recs = original_system.get_recommendations_ultra_fast(
                            user_id, user_profiles[user_id], model_data['similarity_dict'], top_n
                        )
                        original_recs_by_date[date_str] = [rec['product_name'] for rec in original_recs]
                    else:
                        original_recs_by_date[date_str] = []
                
                # Temporal system recommendations
                temporal_recs = temporal_system.get_temporal_recommendations(user_id, test_date, top_n)
                temporal_recs_by_date[date_str] = [rec['product_name'] for rec in temporal_recs]
            
            comparison_results['original_recommendations'][user_id] = original_recs_by_date
            comparison_results['temporal_recommendations'][user_id] = temporal_recs_by_date
            
            # Calculate diversity metrics
            comparison_results['diversity_metrics'][user_id] = self._calculate_diversity_metrics(
                original_recs_by_date, temporal_recs_by_date
            )
            
            # Get temporal insights
            comparison_results['user_insights'][user_id] = temporal_system.get_temporal_insights(user_id)
        
        # Calculate overall metrics
        comparison_results['overall_metrics'] = self._calculate_overall_metrics(comparison_results)
        
        return comparison_results
    
    def _calculate_diversity_metrics(self, original_recs, temporal_recs):
        """Calculate diversity metrics between original and temporal recommendations"""
        
        # Original system diversity
        original_all_items = set()
        original_similarities = []
        original_dates = list(original_recs.keys())
        
        for date_recs in original_recs.values():
            original_all_items.update(date_recs)
        
        for i in range(1, len(original_dates)):
            prev_recs = set(original_recs[original_dates[i-1]])
            curr_recs = set(original_recs[original_dates[i]])
            
            if len(prev_recs) > 0 and len(curr_recs) > 0:
                intersection = len(prev_recs.intersection(curr_recs))
                union = len(prev_recs.union(curr_recs))
                similarity = intersection / union if union > 0 else 0
                original_similarities.append(similarity)
        
        # Temporal system diversity
        temporal_all_items = set()
        temporal_similarities = []
        temporal_dates = list(temporal_recs.keys())
        
        for date_recs in temporal_recs.values():
            temporal_all_items.update(date_recs)
        
        for i in range(1, len(temporal_dates)):
            prev_recs = set(temporal_recs[temporal_dates[i-1]])
            curr_recs = set(temporal_recs[temporal_dates[i]])
            
            if len(prev_recs) > 0 and len(curr_recs) > 0:
                intersection = len(prev_recs.intersection(curr_recs))
                union = len(prev_recs.union(curr_recs))
                similarity = intersection / union if union > 0 else 0
                temporal_similarities.append(similarity)
        
        return {
            'original': {
                'unique_items': len(original_all_items),
                'avg_similarity': np.mean(original_similarities) if original_similarities else 0,
                'diversity_score': 1 - np.mean(original_similarities) if original_similarities else 1
            },
            'temporal': {
                'unique_items': len(temporal_all_items),
                'avg_similarity': np.mean(temporal_similarities) if temporal_similarities else 0,
                'diversity_score': 1 - np.mean(temporal_similarities) if temporal_similarities else 1
            },
            'improvement': {
                'unique_items_increase': len(temporal_all_items) - len(original_all_items),
                'diversity_improvement': (1 - np.mean(temporal_similarities) if temporal_similarities else 1) - 
                                       (1 - np.mean(original_similarities) if original_similarities else 1)
            }
        }
    
    def _calculate_overall_metrics(self, comparison_results):
        """Calculate overall comparison metrics"""
        
        all_diversity_metrics = list(comparison_results['diversity_metrics'].values())
        
        if not all_diversity_metrics:
            return {}
        
        # Average improvements
        avg_unique_items_improvement = np.mean([
            metrics['improvement']['unique_items_increase'] 
            for metrics in all_diversity_metrics
        ])
        
        avg_diversity_improvement = np.mean([
            metrics['improvement']['diversity_improvement'] 
            for metrics in all_diversity_metrics
        ])
        
        # Original vs temporal averages
        avg_original_diversity = np.mean([
            metrics['original']['diversity_score'] 
            for metrics in all_diversity_metrics
        ])
        
        avg_temporal_diversity = np.mean([
            metrics['temporal']['diversity_score'] 
            for metrics in all_diversity_metrics
        ])
        
        return {
            'users_tested': len(all_diversity_metrics),
            'avg_unique_items_improvement': avg_unique_items_improvement,
            'avg_diversity_improvement': avg_diversity_improvement,
            'avg_original_diversity_score': avg_original_diversity,
            'avg_temporal_diversity_score': avg_temporal_diversity,
            'relative_diversity_improvement': (avg_temporal_diversity - avg_original_diversity) / avg_original_diversity if avg_original_diversity > 0 else 0
        }
    
    def print_comparison_results(self, results):
        """Print detailed comparison results"""
        
        print("\n📊 TEMPORAL RECOMMENDATION SYSTEM COMPARISON RESULTS")
        print("="*70)
        
        overall = results['overall_metrics']
        if overall:
            print(f"👥 Users tested: {overall['users_tested']}")
            print(f"📈 Average unique items improvement: {overall['avg_unique_items_improvement']:.2f}")
            print(f"📈 Average diversity improvement: {overall['avg_diversity_improvement']:.4f}")
            print(f"📊 Original system diversity score: {overall['avg_original_diversity_score']:.4f}")
            print(f"📊 Temporal system diversity score: {overall['avg_temporal_diversity_score']:.4f}")
            print(f"🎯 Relative diversity improvement: {overall['relative_diversity_improvement']*100:.1f}%")
        
        print("\n👤 USER-LEVEL ANALYSIS:")
        print("-"*50)
        
        for user_id, diversity_metrics in results['diversity_metrics'].items():
            print(f"\nUser {user_id}:")
            print(f"  Original: {diversity_metrics['original']['unique_items']} unique items, "
                  f"diversity score: {diversity_metrics['original']['diversity_score']:.3f}")
            print(f"  Temporal: {diversity_metrics['temporal']['unique_items']} unique items, "
                  f"diversity score: {diversity_metrics['temporal']['diversity_score']:.3f}")
            print(f"  Improvement: +{diversity_metrics['improvement']['unique_items_increase']} items, "
                  f"+{diversity_metrics['improvement']['diversity_improvement']:.3f} diversity")
        
        print("\n🔍 SAMPLE RECOMMENDATIONS:")
        print("-"*50)
        
        # Show sample recommendations for first user
        first_user = list(results['original_recommendations'].keys())[0]
        original_recs = results['original_recommendations'][first_user]
        temporal_recs = results['temporal_recommendations'][first_user]
        
        print(f"\nUser {first_user} - Sample Days:")
        
        sample_dates = list(original_recs.keys())[:3]  # Show first 3 days
        for date in sample_dates:
            print(f"\n📅 {date}:")
            print(f"  Original: {original_recs.get(date, [])}")
            print(f"  Temporal: {temporal_recs.get(date, [])}")
    
    def run_comprehensive_evaluation(self, test_users=None):
        """Run comprehensive temporal evaluation"""
        
        print("🚀 RUNNING COMPREHENSIVE TEMPORAL EVALUATION")
        print("="*70)
        
        start_time = time.time()
        
        # Run comparison
        results = self.compare_systems(test_users=test_users, test_days=7, top_n=5)
        
        # Print results
        self.print_comparison_results(results)
        
        # Additional insights
        print("\n💡 TEMPORAL INSIGHTS:")
        print("-"*50)
        
        for user_id, insights in results['user_insights'].items():
            if 'error' not in insights:
                print(f"\nUser {user_id}:")
                print(f"  Temporal weight: {insights['temporal_profile_summary']['total_temporal_weight']:.2f}")
                print(f"  Unique items: {insights['temporal_profile_summary']['unique_items']}")
                print(f"  Items due for repurchase: {len(insights['items_due_for_repurchase'])}")
                print(f"  Purchase cycles tracked: {insights['purchase_cycles_tracked']}")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ Evaluation completed in {total_time:.2f} seconds")
        
        return results
