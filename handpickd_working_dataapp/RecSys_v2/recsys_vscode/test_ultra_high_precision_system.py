#!/usr/bin/env python3
"""
🎯 TEST ULTRA HIGH PRECISION SYSTEM FOR 50%+ ACCURACY

This script tests the ultra high precision system that focuses on proven patterns
with very high confidence thresholds to achieve 50%+ precision and recall.

ULTRA FOCUSED STRATEGY:
1. Target only top 10% most predictable users (ultra high confidence)
2. Use proven reorder patterns with 80%+ consistency
3. High confidence categories with 80%+ repeat rates
4. Ultra reliable products (top 20% reorder rates)
5. Very high confidence thresholds (0.5+ minimum)
6. Conservative approach to ensure quality over quantity

TARGET: 50%+ precision AND 50%+ recall through ultra-focused approach
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from ultra_high_precision_system import UltraHighPrecisionSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the ultra high precision system"""
    
    print("🎯 TESTING ULTRA HIGH PRECISION SYSTEM FOR 50%+ ACCURACY")
    print("="*80)
    print("🚀 STRATEGY: Ultra-focused on proven patterns with high confidence")
    print("📊 TARGET: 50%+ precision AND 50%+ recall")
    print("🔬 APPROACH: Quality over quantity - conservative recommendations")
    print("="*80)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    print(f"✅ Date range: {customer_orders_rc['delivery_date'].min().strftime('%Y-%m-%d')} to {customer_orders_rc['delivery_date'].max().strftime('%Y-%m-%d')}")
    
    # Initialize ultra high precision system
    print("\n🎯 INITIALIZING ULTRA HIGH PRECISION SYSTEM...")
    ultra_system = UltraHighPrecisionSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING ULTRA HIGH PRECISION SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    ultra_metrics = ultra_system.calculate_precision_recall_ultra_high(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Performance analysis
    print(f"\n📈 ULTRA HIGH PRECISION SYSTEM PERFORMANCE:")
    print(f"   🎯 Overall Precision: {ultra_metrics['avg_precision']:.4f} ({ultra_metrics['avg_precision']*100:.1f}%)")
    print(f"   🎯 Overall Recall: {ultra_metrics['avg_recall']:.4f} ({ultra_metrics['avg_recall']*100:.1f}%)")
    print(f"   🎯 Overall F1-Score: {ultra_metrics['avg_f1']:.4f} ({ultra_metrics['avg_f1']*100:.1f}%)")
    
    # Ultra high confidence user performance
    print(f"\n🎯 ULTRA HIGH CONFIDENCE USER PERFORMANCE:")
    print(f"   📊 Ultra High Users Precision: {ultra_metrics['ultra_high_precision']:.4f} ({ultra_metrics['ultra_high_precision']*100:.1f}%)")
    print(f"   📊 Ultra High Users Recall: {ultra_metrics['ultra_high_recall']:.4f} ({ultra_metrics['ultra_high_recall']*100:.1f}%)")
    print(f"   👥 Ultra High Users Tested: {ultra_metrics['ultra_high_users_tested']}")
    
    print(f"\n👥 REGULAR USER PERFORMANCE:")
    print(f"   📊 Regular Users Precision: {ultra_metrics['regular_precision']:.4f} ({ultra_metrics['regular_precision']*100:.1f}%)")
    print(f"   📊 Regular Users Recall: {ultra_metrics['regular_recall']:.4f} ({ultra_metrics['regular_recall']*100:.1f}%)")
    print(f"   👥 Regular Users Tested: {ultra_metrics['num_users_tested'] - ultra_metrics['ultra_high_users_tested']}")
    
    # Compare with previous systems
    baseline_precision = 0.22  # Original baseline
    enhanced_precision = 0.303  # Enhanced system
    advanced_precision = 0.0698  # Advanced system (failed)
    
    print(f"\n📊 IMPROVEMENT ANALYSIS:")
    print(f"   📈 vs Baseline (~22%): {ultra_metrics['avg_precision'] - baseline_precision:+.4f} ({(ultra_metrics['avg_precision'] - baseline_precision)*100:+.1f}%)")
    print(f"   📈 vs Enhanced (30.3%): {ultra_metrics['avg_precision'] - enhanced_precision:+.4f} ({(ultra_metrics['avg_precision'] - enhanced_precision)*100:+.1f}%)")
    print(f"   📈 vs Advanced (7.0%): {ultra_metrics['avg_precision'] - advanced_precision:+.4f} ({(ultra_metrics['avg_precision'] - advanced_precision)*100:+.1f}%)")
    
    # Target achievement analysis
    target_precision = 0.50
    target_recall = 0.50
    
    overall_precision_achieved = ultra_metrics['avg_precision'] >= target_precision
    overall_recall_achieved = ultra_metrics['avg_recall'] >= target_recall
    ultra_precision_achieved = ultra_metrics['ultra_high_precision'] >= target_precision
    ultra_recall_achieved = ultra_metrics['ultra_high_recall'] >= target_recall
    
    print(f"\n🎯 TARGET ACHIEVEMENT ANALYSIS:")
    print(f"   Overall 50% precision: {'✅ ACHIEVED' if overall_precision_achieved else '❌ NOT ACHIEVED'} "
          f"({ultra_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Overall 50% recall: {'✅ ACHIEVED' if overall_recall_achieved else '❌ NOT ACHIEVED'} "
          f"({ultra_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    
    print(f"\n🎯 ULTRA HIGH CONFIDENCE USERS TARGET:")
    print(f"   Ultra High 50% precision: {'✅ ACHIEVED' if ultra_precision_achieved else '❌ NOT ACHIEVED'} "
          f"({ultra_metrics['ultra_high_precision']*100:.1f}% vs 50.0%)")
    print(f"   Ultra High 50% recall: {'✅ ACHIEVED' if ultra_recall_achieved else '❌ NOT ACHIEVED'} "
          f"({ultra_metrics['ultra_high_recall']*100:.1f}% vs 50.0%)")
    
    # Performance distribution analysis
    precision_scores = ultra_metrics['precision_scores']
    recall_scores = ultra_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"\n📊 PERFORMANCE DISTRIBUTION:")
        
        # Precision distribution
        high_precision_users = sum(1 for p in precision_scores if p >= 0.5)
        medium_precision_users = sum(1 for p in precision_scores if 0.3 <= p < 0.5)
        low_precision_users = sum(1 for p in precision_scores if p < 0.3)
        
        print(f"   📈 Precision Distribution:")
        print(f"      High (≥50%): {high_precision_users:,} users ({high_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Medium (30-49%): {medium_precision_users:,} users ({medium_precision_users/len(precision_scores)*100:.1f}%)")
        print(f"      Low (<30%): {low_precision_users:,} users ({low_precision_users/len(precision_scores)*100:.1f}%)")
        
        # Both metrics high
        both_high_users = sum(1 for p, r in zip(precision_scores, recall_scores) if p >= 0.5 and r >= 0.5)
        print(f"   🎯 Both precision AND recall ≥50%: {both_high_users:,} users ({both_high_users/len(precision_scores)*100:.1f}%)")
    
    # Sample ultra high precision recommendations
    print(f"\n🔍 SAMPLE ULTRA HIGH PRECISION RECOMMENDATIONS:")
    print("-"*70)
    
    # Get ultra high confidence users for testing
    ultra_high_users = list(ultra_system.ultra_high_confidence_users)[:3] if ultra_system.ultra_high_confidence_users else []
    
    if ultra_high_users:
        for i, user_id in enumerate(ultra_high_users):
            print(f"\n👤 Ultra High Confidence User {i+1}: {user_id[:8]}...")
            
            # Generate ultra high precision recommendations
            recommendations = ultra_system.get_ultra_high_precision_recommendations(user_id, max_date)
            
            print(f"   🎯 Ultra high precision recommendations ({len(recommendations)} items):")
            for j, rec in enumerate(recommendations):
                print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, "
                      f"Confidence: {rec['confidence_level']}, Cat: {rec['category']})")
            
            if not recommendations:
                print(f"      ⚠️ No ultra high confidence recommendations (high threshold)")
    else:
        print(f"   ⚠️ No ultra high confidence users identified")
    
    # System effectiveness analysis
    print(f"\n💡 SYSTEM EFFECTIVENESS ANALYSIS:")
    print("-"*70)
    
    if overall_precision_achieved and overall_recall_achieved:
        print(f"🎉 BREAKTHROUGH SUCCESS: 50%+ TARGET ACHIEVED!")
        print(f"   ✅ Ultra high precision strategy successful")
        print(f"   🎯 Conservative approach with high confidence thresholds works")
        print(f"   📊 Quality over quantity strategy validated")
        
    elif ultra_precision_achieved or ultra_recall_achieved:
        print(f"📈 PARTIAL SUCCESS: Ultra high confidence users achieving 50%+")
        print(f"   🎯 Strategy working for most predictable users")
        print(f"   📊 Need to expand ultra high confidence user identification")
        print(f"   🔧 Consider lowering thresholds slightly for broader coverage")
        
    elif ultra_metrics['avg_precision'] >= 0.40:
        print(f"📊 SIGNIFICANT PROGRESS: 40%+ precision achieved")
        print(f"   🎯 Ultra focused approach showing promise")
        print(f"   📈 Better than complex ensemble approaches")
        print(f"   🔧 Fine-tune confidence thresholds for 50% target")
        
    elif ultra_metrics['avg_precision'] >= enhanced_precision:
        print(f"📈 MAINTAINED PERFORMANCE: Matching enhanced system")
        print(f"   ✅ Conservative approach prevents degradation")
        print(f"   🎯 Ultra high confidence users show potential")
        print(f"   🔧 Need to optimize pattern recognition")
        
    else:
        print(f"🔧 OPTIMIZATION NEEDED")
        print(f"   🔍 Ultra focused approach may be too restrictive")
        print(f"   📊 Consider expanding ultra high confidence criteria")
        print(f"   🎯 Balance between precision and coverage needed")
    
    # Business recommendations
    print(f"\n💼 BUSINESS RECOMMENDATIONS:")
    print("-"*70)
    
    if ultra_metrics['ultra_high_users_tested'] > 0:
        ultra_coverage = ultra_metrics['ultra_high_users_tested'] / ultra_metrics['num_users_tested']
        print(f"   📊 Ultra high confidence coverage: {ultra_coverage:.1%} of users")
        
        if ultra_precision_achieved:
            print(f"   🚀 DEPLOY for ultra high confidence users immediately")
            print(f"   📈 Expand ultra high confidence identification criteria")
            print(f"   🎯 Use enhanced system for remaining users")
        elif ultra_metrics['ultra_high_precision'] >= 0.40:
            print(f"   📊 PILOT with ultra high confidence users")
            print(f"   🔧 Optimize thresholds based on pilot results")
            print(f"   📈 Gradually expand coverage")
        else:
            print(f"   🔍 RESEARCH ultra high confidence criteria")
            print(f"   📊 Analyze why ultra focused approach isn't working")
            print(f"   🎯 Consider hybrid approach")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"🎉 ULTRA HIGH PRECISION SYSTEM TEST COMPLETE!")
    
    if overall_precision_achieved and overall_recall_achieved:
        print(f"✅ BREAKTHROUGH: 50%+ TARGET ACHIEVED!")
        print(f"🚀 Overall: Precision {ultra_metrics['avg_precision']*100:.1f}% | Recall {ultra_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 READY FOR PRODUCTION DEPLOYMENT")
        
    elif ultra_precision_achieved and ultra_recall_achieved:
        print(f"📈 PARTIAL SUCCESS: Ultra high confidence users achieve 50%+")
        print(f"🎯 Ultra High: Precision {ultra_metrics['ultra_high_precision']*100:.1f}% | Recall {ultra_metrics['ultra_high_recall']*100:.1f}%")
        print(f"📊 Expand ultra high confidence identification")
        
    elif ultra_metrics['avg_precision'] >= 0.40:
        print(f"📊 SIGNIFICANT PROGRESS: 40%+ precision achieved")
        print(f"🚀 Overall: Precision {ultra_metrics['avg_precision']*100:.1f}% | Recall {ultra_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Fine-tune for 50% target")
        
    else:
        print(f"🔧 CONTINUED OPTIMIZATION NEEDED")
        print(f"📊 Current: Precision {ultra_metrics['avg_precision']*100:.1f}% | Recall {ultra_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*80)
    
    return ultra_metrics

if __name__ == "__main__":
    results = main()
