#!/usr/bin/env python3
"""
🎯 FOCUSED HIGH-PERFORMANCE RECOMMENDATION SYSTEM

Root Cause Analysis of 1% Performance:
1. Over-complex ensemble diluting strong signals
2. Not leveraging the 7-day median reorder interval effectively
3. Missing the power of recent purchase patterns
4. Not exploiting the limited SKU universe (300-400 products)

NEW APPROACH - FOCUSED ON PROVEN PATTERNS:
1. Recent Purchase Amplification (last 30 days get 5x weight)
2. Reorder Cycle Prediction (7-day median interval)
3. Category Stickiness (99.8% repeat rate)
4. Product Frequency in Limited SKU Space
5. Simple but Powerful Scoring

TARGET: 50%+ precision/recall through focused approach
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class FocusedHighPerformanceSystem:
    """Focused system targeting 50%+ accuracy through proven patterns"""
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        self.product_name_column = product_name_column
        
        print(f"🎯 FOCUSED HIGH-PERFORMANCE RECOMMENDATION SYSTEM")
        print(f"🚀 APPROACH: Simple but powerful - focus on proven patterns")
        print(f"📊 TARGET: 50%+ precision/recall")
        
        # Core data structures
        self.sku_to_name_mapping = {}
        self.name_to_sku_mapping = {}
        self.item_categories = {}
        self.catalogue_lookup = {}
        
        # High-impact features
        self.user_recent_patterns = {}  # Last 30 days patterns
        self.user_reorder_cycles = {}   # 7-day cycle patterns
        self.category_loyalty = {}      # 99.8% repeat rate
        self.product_popularity = {}    # In limited SKU space
        
        # User repurchase ratios
        self.user_repurchase_ratios = {}
        self.default_repurchase_ratio = 0.7
        
        if user_repurchase_ratios_df is not None:
            self.load_user_repurchase_ratios(user_repurchase_ratios_df)
    
    def load_user_repurchase_ratios(self, repurchase_ratios_df):
        """Load user-specific repurchase ratios"""
        self.user_repurchase_ratios = dict(zip(
            repurchase_ratios_df['customer_id'], 
            repurchase_ratios_df['repurchase_ratio']
        ))
        print(f"✅ Loaded repurchase ratios for {len(self.user_repurchase_ratios):,} users")
    
    def build_focused_features(self, customer_orders_df, catalogue_df, reference_date):
        """Build focused features for high performance"""
        print(f"🔧 BUILDING FOCUSED HIGH-IMPACT FEATURES...")
        
        # Basic mappings
        for _, row in catalogue_df.iterrows():
            product_name = row[self.product_name_column]
            sku_code = row['sku_code']
            
            self.sku_to_name_mapping[sku_code] = product_name
            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)
        
        # Convert orders to use product names
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Item categories
        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()
        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))
        
        # Catalogue lookup
        for _, row in clean_catalogue.iterrows():
            product_name = row[self.product_name_column]
            self.catalogue_lookup[product_name] = {
                'name': product_name,
                'category_name': row['category_name'],
                'sku_codes': self.name_to_sku_mapping[product_name]
            }
        
        # Build high-impact features
        self._build_recent_patterns(orders_with_names, reference_date)
        self._build_reorder_cycles(orders_with_names, reference_date)
        self._build_category_loyalty(orders_with_names)
        self._build_product_popularity(orders_with_names)
        
        print(f"✅ Focused features built successfully")
    
    def _build_recent_patterns(self, orders_with_names, reference_date):
        """Build recent purchase patterns (last 30 days get 5x weight)"""
        print(f"   📅 Building recent patterns (30-day focus)...")
        
        cutoff_date = reference_date - timedelta(days=30)
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            recent_orders = customer_orders[customer_orders['delivery_date'] >= cutoff_date]
            
            if len(recent_orders) > 0:
                # Recent product frequencies with recency weighting
                recent_products = {}
                for _, order in recent_orders.iterrows():
                    product = order['product_name']
                    days_ago = (reference_date - order['delivery_date']).days
                    
                    # Recency weight: more recent = higher weight
                    recency_weight = max(0.1, 1.0 - (days_ago / 30.0))
                    
                    if product not in recent_products:
                        recent_products[product] = 0
                    recent_products[product] += recency_weight
                
                # Recent categories
                recent_categories = {}
                for product, weight in recent_products.items():
                    if product in self.item_categories:
                        category = self.item_categories[product]
                        if category not in recent_categories:
                            recent_categories[category] = 0
                        recent_categories[category] += weight
                
                self.user_recent_patterns[customer_id] = {
                    'products': recent_products,
                    'categories': recent_categories,
                    'total_weight': sum(recent_products.values())
                }
        
        print(f"      ✅ Built recent patterns for {len(self.user_recent_patterns)} users")
    
    def _build_reorder_cycles(self, orders_with_names, reference_date):
        """Build reorder cycle predictions (7-day median interval)"""
        print(f"   🔄 Building reorder cycles (7-day focus)...")
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            user_cycles = {}
            
            for product_name, product_orders in customer_orders.groupby('product_name'):
                if len(product_orders) >= 2:
                    sorted_orders = product_orders.sort_values('delivery_date')
                    
                    # Calculate intervals
                    intervals = []
                    for i in range(1, len(sorted_orders)):
                        interval = (sorted_orders.iloc[i]['delivery_date'] - 
                                  sorted_orders.iloc[i-1]['delivery_date']).days
                        intervals.append(interval)
                    
                    if intervals:
                        median_interval = np.median(intervals)
                        last_order_date = sorted_orders['delivery_date'].max()
                        days_since_last = (reference_date - last_order_date).days
                        
                        # Reorder probability based on 7-day median insight
                        if median_interval <= 7:  # High frequency items
                            reorder_prob = min(1.0, days_since_last / 7.0)
                        elif median_interval <= 14:  # Medium frequency
                            reorder_prob = min(1.0, days_since_last / 14.0)
                        else:  # Low frequency
                            reorder_prob = min(1.0, days_since_last / median_interval)
                        
                        user_cycles[product_name] = {
                            'median_interval': median_interval,
                            'days_since_last': days_since_last,
                            'reorder_probability': reorder_prob,
                            'order_count': len(sorted_orders)
                        }
            
            if user_cycles:
                self.user_reorder_cycles[customer_id] = user_cycles
        
        print(f"      ✅ Built reorder cycles for {len(self.user_reorder_cycles)} users")
    
    def _build_category_loyalty(self, orders_with_names):
        """Build category loyalty patterns (99.8% repeat rate)"""
        print(f"   📂 Building category loyalty (99.8% repeat rate)...")
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            category_counts = {}
            total_orders = len(customer_orders)
            
            for _, order in customer_orders.iterrows():
                product = order['product_name']
                if product in self.item_categories:
                    category = self.item_categories[product]
                    if category not in category_counts:
                        category_counts[category] = 0
                    category_counts[category] += 1
            
            # Calculate loyalty scores
            category_loyalty = {}
            for category, count in category_counts.items():
                loyalty_score = count / total_orders
                category_loyalty[category] = loyalty_score
            
            self.category_loyalty[customer_id] = category_loyalty
        
        print(f"      ✅ Built category loyalty for {len(self.category_loyalty)} users")
    
    def _build_product_popularity(self, orders_with_names):
        """Build product popularity in limited SKU space"""
        print(f"   📊 Building product popularity (limited SKU space)...")
        
        # Global product popularity
        product_counts = orders_with_names['product_name'].value_counts()
        total_orders = len(orders_with_names)
        
        for product, count in product_counts.items():
            popularity_score = count / total_orders
            self.product_popularity[product] = popularity_score
        
        print(f"      ✅ Built popularity for {len(self.product_popularity)} products")
    
    def get_focused_recommendations(self, user_id, current_date, top_n=8):
        """
        Generate focused recommendations using proven high-impact patterns
        
        SCORING STRATEGY:
        1. Recent Purchase Amplification (40% weight)
        2. Reorder Cycle Prediction (30% weight)  
        3. Category Loyalty (20% weight)
        4. Product Popularity (10% weight)
        """
        
        all_scores = defaultdict(float)
        
        # 1. RECENT PURCHASE AMPLIFICATION (40% weight)
        if user_id in self.user_recent_patterns:
            recent_data = self.user_recent_patterns[user_id]
            
            # Recent products get high scores
            for product, weight in recent_data['products'].items():
                # Don't recommend what they just bought
                continue
            
            # Recent categories get boosted
            for category, weight in recent_data['categories'].items():
                category_products = [p for p, c in self.item_categories.items() if c == category]
                for product in category_products:
                    if product not in recent_data['products']:  # Not recently purchased
                        category_boost = weight / len(category_products) if category_products else 0
                        all_scores[product] += category_boost * 0.4  # 40% weight
        
        # 2. REORDER CYCLE PREDICTION (30% weight)
        if user_id in self.user_reorder_cycles:
            cycles = self.user_reorder_cycles[user_id]
            
            for product, cycle_info in cycles.items():
                reorder_prob = cycle_info['reorder_probability']
                frequency_boost = min(2.0, cycle_info['order_count'] / 3.0)  # More orders = higher boost
                
                reorder_score = reorder_prob * frequency_boost
                all_scores[product] += reorder_score * 0.3  # 30% weight
        
        # 3. CATEGORY LOYALTY (20% weight)
        if user_id in self.category_loyalty:
            loyalty = self.category_loyalty[user_id]
            
            for category, loyalty_score in loyalty.items():
                if loyalty_score > 0.1:  # Significant category preference
                    category_products = [p for p, c in self.item_categories.items() if c == category]
                    for product in category_products:
                        # Boost products in preferred categories
                        loyalty_boost = loyalty_score / len(category_products) if category_products else 0
                        all_scores[product] += loyalty_boost * 0.2  # 20% weight
        
        # 4. PRODUCT POPULARITY (10% weight)
        for product, popularity in self.product_popularity.items():
            if popularity > 0.001:  # Minimum popularity threshold
                all_scores[product] += popularity * 0.1  # 10% weight
        
        # Remove products user has never shown interest in categories
        if user_id in self.category_loyalty:
            user_categories = set(self.category_loyalty[user_id].keys())
            filtered_scores = {}
            for product, score in all_scores.items():
                if product in self.item_categories:
                    product_category = self.item_categories[product]
                    if product_category in user_categories:
                        filtered_scores[product] = score
            all_scores = filtered_scores
        
        # Sort and format recommendations
        sorted_recommendations = sorted(all_scores.items(), key=lambda x: x[1], reverse=True)
        
        final_recommendations = []
        for i, (product_name, score) in enumerate(sorted_recommendations[:top_n]):
            if score > 0:  # Only recommend products with positive scores
                item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
                sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'
                
                final_recommendations.append({
                    'sku_code': sku_code,
                    'product_name': product_name,
                    'category': item_info['category_name'],
                    'score': round(score, 4),
                    'predicted_quantity': 1.0,
                    'recommendation_type': 'focused_high_performance'
                })
        
        return final_recommendations
    
    def calculate_dynamic_k_for_user(self, user_id, customer_orders_df):
        """Calculate dynamic k based on user's typical order size"""
        user_orders = customer_orders_df[customer_orders_df['customer_id'] == user_id]
        
        if len(user_orders) == 0:
            return 8
        
        # Get last 15 order sessions
        last_15_orders = user_orders.nlargest(15, 'delivery_date')
        items_per_order = last_15_orders.groupby('display_order_id').size()
        
        if len(items_per_order) == 0:
            return 8
        
        median_items = int(np.median(items_per_order))
        
        # Focus on smaller, more accurate recommendations
        return max(5, min(12, median_items))
    
    def calculate_precision_recall_focused(self, customer_orders_df, test_start_date, test_end_date):
        """Calculate precision and recall for focused system"""
        print(f"📊 CALCULATING FOCUSED SYSTEM PRECISION AND RECALL")
        print(f"   📅 Test period: {test_start_date.strftime('%Y-%m-%d')} to {test_end_date.strftime('%Y-%m-%d')}")
        
        # Split data
        train_data = customer_orders_df[customer_orders_df['delivery_date'] < test_start_date]
        
        # Test data: orders placed during test period
        test_delivery_start = test_start_date + timedelta(days=1)
        test_delivery_end = test_end_date + timedelta(days=1)
        test_data = customer_orders_df[
            (customer_orders_df['delivery_date'] >= test_delivery_start) & 
            (customer_orders_df['delivery_date'] <= test_delivery_end)
        ]
        
        print(f"   📊 Train orders: {len(train_data):,}")
        print(f"   📊 Test orders: {len(test_data):,}")
        
        # Build focused features on training data
        self.build_focused_features(train_data, pd.read_csv('catalogue_rc_with_parent_names.csv'), test_start_date)
        
        # Convert test data
        test_data_with_names = test_data.copy()
        test_data_with_names['product_name'] = test_data_with_names['sku_code'].map(self.sku_to_name_mapping)
        test_data_with_names = test_data_with_names.dropna(subset=['product_name'])
        test_data_with_names['order_date'] = test_data_with_names['delivery_date'] - timedelta(days=1)
        
        # Group actual orders by order_date
        actual_orders_by_date = {}
        for _, order in test_data_with_names.iterrows():
            order_date = order['order_date']
            user_id = order['customer_id']
            product_name = order['product_name']
            
            if order_date not in actual_orders_by_date:
                actual_orders_by_date[order_date] = {}
            
            if user_id not in actual_orders_by_date[order_date]:
                actual_orders_by_date[order_date][user_id] = set()
            
            actual_orders_by_date[order_date][user_id].add(product_name)
        
        # Calculate metrics
        precision_scores = []
        recall_scores = []
        f1_scores = []
        
        test_dates = [test_start_date + timedelta(days=i) for i in range((test_end_date - test_start_date).days + 1)]
        
        total_comparisons = 0
        for test_date in test_dates:
            if test_date not in actual_orders_by_date:
                continue
                
            users_with_orders = actual_orders_by_date[test_date]
            print(f"   📅 Testing {test_date.strftime('%Y-%m-%d')}: {len(users_with_orders)} users")
            
            for user_id, actual_items in users_with_orders.items():
                # Generate focused recommendations
                dynamic_k = self.calculate_dynamic_k_for_user(user_id, train_data)
                recommendations = self.get_focused_recommendations(user_id, test_date, dynamic_k)
                recommended_items = set([rec['product_name'] for rec in recommendations])
                
                # Calculate metrics
                if len(recommended_items) > 0:
                    precision = len(recommended_items.intersection(actual_items)) / len(recommended_items)
                else:
                    precision = 0.0
                
                if len(actual_items) > 0:
                    recall = len(recommended_items.intersection(actual_items)) / len(actual_items)
                else:
                    recall = 0.0
                
                if precision + recall > 0:
                    f1 = 2 * (precision * recall) / (precision + recall)
                else:
                    f1 = 0.0
                
                precision_scores.append(precision)
                recall_scores.append(recall)
                f1_scores.append(f1)
                total_comparisons += 1
        
        # Calculate overall metrics
        avg_precision = np.mean(precision_scores) if precision_scores else 0.0
        avg_recall = np.mean(recall_scores) if recall_scores else 0.0
        avg_f1 = np.mean(f1_scores) if f1_scores else 0.0
        
        print(f"✅ FOCUSED SYSTEM PRECISION & RECALL RESULTS:")
        print(f"   📊 Average Precision: {avg_precision:.4f}")
        print(f"   📊 Average Recall: {avg_recall:.4f}")
        print(f"   📊 Average F1-Score: {avg_f1:.4f}")
        print(f"   👥 User-date combinations tested: {total_comparisons}")
        
        return {
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'avg_f1': avg_f1,
            'num_users_tested': total_comparisons,
            'precision_scores': precision_scores,
            'recall_scores': recall_scores,
            'f1_scores': f1_scores
        }
