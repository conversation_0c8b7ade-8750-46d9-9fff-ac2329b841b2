#!/usr/bin/env python3
"""
🚀 TEST ENHANCED RECOMMENDATION SYSTEM FOR 50%+ ACCURACY

This script tests the enhanced recommendation system that implements:
1. Category-aware recommendations (leveraging 99.8% repeat rate)
2. Product co-occurrence patterns (strong associations like Coriander+Lemon)
3. Customer archetype-specific strategies
4. Collaborative filtering with similar customers
5. Reorder pattern predictions
6. Ensemble approach combining all methods

TARGET: Achieve 50%+ precision and recall
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from enhanced_recommendation_system import EnhancedRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

def main():
    """Test the enhanced recommendation system"""
    
    print("🚀 TESTING ENHANCED RECOMMENDATION SYSTEM")
    print("="*70)
    print("🎯 TARGET: 50%+ precision and recall")
    print("📊 APPROACH: Multi-strategy ensemble system")
    print("🔬 BASED ON: Advanced EDA insights")
    print("="*70)
    
    # Load data
    print("\n📊 LOADING DATA...")
    catalogue_rc = pd.read_csv('catalogue_rc_with_parent_names.csv')
    customer_orders_rc = pd.read_csv('customer_orders.csv')
    repurchase_ratios_df = pd.read_csv('repurchase_ratios.csv')
    
    # Convert delivery_date to datetime
    customer_orders_rc['delivery_date'] = pd.to_datetime(customer_orders_rc['delivery_date'])
    
    print(f"✅ Loaded {len(customer_orders_rc):,} orders")
    print(f"✅ Loaded {len(catalogue_rc):,} products")
    print(f"✅ Loaded {len(repurchase_ratios_df):,} user profiles")
    
    # Initialize enhanced system
    print("\n🚀 INITIALIZING ENHANCED RECOMMENDATION SYSTEM...")
    enhanced_system = EnhancedRecommendationSystem(
        user_repurchase_ratios_df=repurchase_ratios_df,
        product_name_column='name'
    )
    
    # Test precision and recall
    print("\n📊 TESTING ENHANCED SYSTEM PERFORMANCE...")
    max_date = customer_orders_rc['delivery_date'].max()
    test_end_date = max_date
    test_start_date = max_date - timedelta(days=7)
    
    enhanced_metrics = enhanced_system.calculate_precision_recall_enhanced(
        customer_orders_rc, test_start_date, test_end_date
    )
    
    # Compare with baseline (if available)
    print(f"\n📈 PERFORMANCE COMPARISON:")
    print(f"   🚀 Enhanced System:")
    print(f"      Precision: {enhanced_metrics['avg_precision']:.4f} ({enhanced_metrics['avg_precision']*100:.1f}%)")
    print(f"      Recall: {enhanced_metrics['avg_recall']:.4f} ({enhanced_metrics['avg_recall']*100:.1f}%)")
    print(f"      F1-Score: {enhanced_metrics['avg_f1']:.4f} ({enhanced_metrics['avg_f1']*100:.1f}%)")
    
    # Check if target achieved
    target_precision = 0.50
    target_recall = 0.50
    
    precision_achieved = enhanced_metrics['avg_precision'] >= target_precision
    recall_achieved = enhanced_metrics['avg_recall'] >= target_recall
    
    print(f"\n🎯 TARGET ACHIEVEMENT:")
    print(f"   Precision Target (50%): {'✅ ACHIEVED' if precision_achieved else '❌ NOT ACHIEVED'} "
          f"({enhanced_metrics['avg_precision']*100:.1f}% vs 50.0%)")
    print(f"   Recall Target (50%): {'✅ ACHIEVED' if recall_achieved else '❌ NOT ACHIEVED'} "
          f"({enhanced_metrics['avg_recall']*100:.1f}% vs 50.0%)")
    
    # Detailed analysis
    print(f"\n📊 DETAILED PERFORMANCE ANALYSIS:")
    precision_scores = enhanced_metrics['precision_scores']
    recall_scores = enhanced_metrics['recall_scores']
    
    if precision_scores and recall_scores:
        print(f"   📈 Precision Distribution:")
        print(f"      Min: {min(precision_scores):.4f}, Max: {max(precision_scores):.4f}")
        print(f"      Std: {np.std(precision_scores):.4f}")
        print(f"      Users with >50% precision: {sum(1 for p in precision_scores if p > 0.5):,} "
              f"({sum(1 for p in precision_scores if p > 0.5)/len(precision_scores)*100:.1f}%)")
        
        print(f"   📈 Recall Distribution:")
        print(f"      Min: {min(recall_scores):.4f}, Max: {max(recall_scores):.4f}")
        print(f"      Std: {np.std(recall_scores):.4f}")
        print(f"      Users with >50% recall: {sum(1 for r in recall_scores if r > 0.5):,} "
              f"({sum(1 for r in recall_scores if r > 0.5)/len(recall_scores)*100:.1f}%)")
    
    # Test sample recommendations
    print(f"\n🔍 SAMPLE ENHANCED RECOMMENDATIONS:")
    print("-"*50)
    
    # Get some test users
    test_users = customer_orders_rc['customer_id'].value_counts().head(5).index.tolist()
    
    for i, user_id in enumerate(test_users[:3]):
        print(f"\n👤 User {i+1}: {user_id[:8]}...")
        
        # Get user's recent purchases for context
        user_orders = customer_orders_rc[customer_orders_rc['customer_id'] == user_id]
        recent_orders = user_orders[user_orders['delivery_date'] >= max_date - timedelta(days=30)]
        
        if len(recent_orders) > 0:
            # Map to product names
            sku_to_name = dict(zip(catalogue_rc['sku_code'], catalogue_rc['name']))
            recent_products = recent_orders['sku_code'].map(sku_to_name).dropna().unique()
            
            print(f"   📅 Recent purchases (last 30 days): {list(recent_products)[:5]}")
            
            # Get enhanced recommendations
            recommendations = enhanced_system.get_enhanced_recommendations(
                user_id, max_date, customer_orders_rc
            )
            
            print(f"   🚀 Enhanced recommendations ({len(recommendations)} items):")
            for j, rec in enumerate(recommendations[:5]):
                print(f"      {j+1}. {rec['product_name']} (Score: {rec['score']:.3f}, Category: {rec['category']})")
        else:
            print(f"   ⚠️ No recent orders found for this user")
    
    # Strategy effectiveness analysis
    print(f"\n💡 STRATEGY EFFECTIVENESS INSIGHTS:")
    print("-"*50)
    
    if enhanced_metrics['avg_precision'] >= 0.40:
        print(f"✅ STRONG PERFORMANCE (40%+ precision achieved)")
        print(f"   🎯 Key success factors likely include:")
        print(f"      • Category-aware recommendations (99.8% repeat rate)")
        print(f"      • Product co-occurrence patterns")
        print(f"      • Customer archetype strategies")
    elif enhanced_metrics['avg_precision'] >= 0.30:
        print(f"📈 GOOD IMPROVEMENT (30%+ precision achieved)")
        print(f"   🔧 Further optimization needed:")
        print(f"      • Fine-tune ensemble weights")
        print(f"      • Enhance collaborative filtering")
        print(f"      • Improve reorder pattern detection")
    else:
        print(f"⚠️ NEEDS OPTIMIZATION (Below 30% precision)")
        print(f"   🔍 Root cause analysis required:")
        print(f"      • Check feature computation")
        print(f"      • Validate co-occurrence patterns")
        print(f"      • Review archetype classification")
    
    # Final summary
    print(f"\n" + "="*70)
    print(f"🎉 ENHANCED RECOMMENDATION SYSTEM TEST COMPLETE!")
    
    if precision_achieved and recall_achieved:
        print(f"✅ SUCCESS: 50%+ TARGET ACHIEVED!")
        print(f"🚀 Precision: {enhanced_metrics['avg_precision']*100:.1f}% | Recall: {enhanced_metrics['avg_recall']*100:.1f}%")
        print(f"📈 Ready for production deployment")
    elif enhanced_metrics['avg_precision'] >= 0.40 or enhanced_metrics['avg_recall'] >= 0.40:
        print(f"📈 SIGNIFICANT IMPROVEMENT ACHIEVED!")
        print(f"🚀 Precision: {enhanced_metrics['avg_precision']*100:.1f}% | Recall: {enhanced_metrics['avg_recall']*100:.1f}%")
        print(f"🔧 Close to target - minor optimizations needed")
    else:
        print(f"🔧 FURTHER OPTIMIZATION REQUIRED")
        print(f"📊 Current: Precision {enhanced_metrics['avg_precision']*100:.1f}% | Recall {enhanced_metrics['avg_recall']*100:.1f}%")
        print(f"🎯 Target: 50%+ for both metrics")
    
    print(f"="*70)
    
    return enhanced_metrics

if __name__ == "__main__":
    results = main()
