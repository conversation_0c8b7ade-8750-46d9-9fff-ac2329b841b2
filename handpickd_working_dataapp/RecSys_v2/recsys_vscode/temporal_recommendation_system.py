import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import time
import hashlib
import warnings
warnings.filterwarnings('ignore')

class TemporalAwareRecommendationSystem:
    """
    🕒 TEMPORAL-AWARE RECOMMENDATION SYSTEM
    
    Addresses the critical issue of static recommendations by implementing:
    1. Time-based user profiling with purchase frequency patterns
    2. Recency-weighted scoring with temporal decay
    3. Seasonality and availability awareness
    4. Dynamic recommendation diversification across time periods
    5. Purchase cycle prediction and timing optimization
    """
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name', 
                 temporal_window_days=90, recency_decay_factor=0.1):
        """
        Initialize temporal-aware recommendation system
        
        Args:
            user_repurchase_ratios_df: DataFrame with user repurchase ratios
            product_name_column: Column name for product identification
            temporal_window_days: Days to consider for temporal patterns (default: 90)
            recency_decay_factor: Decay factor for recency weighting (default: 0.1)
        """
        # Configuration
        self.product_name_column = product_name_column
        self.temporal_window_days = temporal_window_days
        self.recency_decay_factor = recency_decay_factor
        
        print(f"🕒 Initializing Temporal-Aware Recommendation System")
        print(f"   📊 Product column: '{product_name_column}'")
        print(f"   ⏰ Temporal window: {temporal_window_days} days")
        print(f"   📉 Recency decay factor: {recency_decay_factor}")
        
        # Static data structures
        self.item_categories = {}
        self.item_purchase_frequency = {}
        self.item_avg_quantities = {}
        self.catalogue_lookup = {}
        self.name_to_sku_mapping = {}
        self.sku_to_name_mapping = {}
        
        # Temporal data structures
        self.user_temporal_profiles = {}
        self.item_seasonality_patterns = {}
        self.user_purchase_cycles = {}
        self.temporal_similarity_cache = {}
        
        # Caching system
        self.cache = {}
        self.popular_items = []
        
        # User-specific repurchase ratios
        self.user_repurchase_ratios = {}
        self.default_repurchase_ratio = 0.7
        
        if user_repurchase_ratios_df is not None:
            self.load_user_repurchase_ratios(user_repurchase_ratios_df)
    
    def load_user_repurchase_ratios(self, repurchase_ratios_df):
        """Load user-specific repurchase ratios from DataFrame"""
        self.user_repurchase_ratios = dict(zip(
            repurchase_ratios_df['customer_id'], 
            repurchase_ratios_df['repurchase_ratio']
        ))
        print(f"✅ Loaded repurchase ratios for {len(self.user_repurchase_ratios):,} users")
    
    def get_user_repurchase_ratio(self, user_id):
        """Get repurchase ratio for a specific user"""
        return self.user_repurchase_ratios.get(user_id, self.default_repurchase_ratio)
    
    def calculate_temporal_decay(self, days_ago, decay_factor=None):
        """
        Calculate temporal decay weight based on days since purchase
        
        Args:
            days_ago: Number of days since the purchase
            decay_factor: Decay factor (uses instance default if None)
            
        Returns:
            float: Decay weight between 0 and 1
        """
        if decay_factor is None:
            decay_factor = self.recency_decay_factor
        
        # Exponential decay: weight = exp(-decay_factor * days_ago)
        return np.exp(-decay_factor * days_ago)
    
    def extract_temporal_patterns(self, customer_orders_df, current_date=None):
        """
        Extract temporal patterns from customer orders
        
        Args:
            customer_orders_df: DataFrame with customer orders
            current_date: Reference date for temporal calculations
        """
        if current_date is None:
            current_date = customer_orders_df['delivery_date'].max()
        
        print(f"🕒 Extracting temporal patterns (reference date: {current_date.strftime('%Y-%m-%d')})")
        
        # Convert orders to use product names
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Calculate days since each order
        orders_with_names['days_ago'] = (current_date - orders_with_names['delivery_date']).dt.days
        
        # Filter to temporal window
        recent_orders = orders_with_names[orders_with_names['days_ago'] <= self.temporal_window_days]
        
        print(f"   📊 Processing {len(recent_orders):,} orders within {self.temporal_window_days} days")
        
        # Extract user temporal profiles
        self._build_user_temporal_profiles(recent_orders, current_date)
        
        # Extract item seasonality patterns
        self._build_item_seasonality_patterns(orders_with_names)
        
        # Extract user purchase cycles
        self._build_user_purchase_cycles(orders_with_names, current_date)
        
        print(f"✅ Temporal pattern extraction complete")
    
    def _build_user_temporal_profiles(self, recent_orders, current_date):
        """Build temporal profiles for users based on recent orders"""
        print(f"   🔄 Building user temporal profiles...")
        
        self.user_temporal_profiles = {}
        
        for user_id, user_orders in recent_orders.groupby('customer_id'):
            # Calculate recency-weighted purchase counts
            temporal_items = defaultdict(float)
            temporal_quantities = defaultdict(list)
            purchase_dates = defaultdict(list)
            
            for _, order in user_orders.iterrows():
                product_name = order['product_name']
                days_ago = order['days_ago']
                quantity = order['ordered_qty']
                
                # Apply temporal decay
                decay_weight = self.calculate_temporal_decay(days_ago)
                temporal_items[product_name] += decay_weight
                temporal_quantities[product_name].append(quantity)
                purchase_dates[product_name].append(order['delivery_date'])
            
            # Calculate average quantities and purchase frequencies
            avg_quantities = {item: np.mean(quantities) for item, quantities in temporal_quantities.items()}
            
            # Calculate purchase frequency (purchases per week)
            purchase_frequencies = {}
            for item, dates in purchase_dates.items():
                if len(dates) > 1:
                    date_range = (max(dates) - min(dates)).days
                    if date_range > 0:
                        purchase_frequencies[item] = len(dates) / (date_range / 7)  # purchases per week
                    else:
                        purchase_frequencies[item] = 1.0  # Single day, assume weekly
                else:
                    purchase_frequencies[item] = 1.0  # Single purchase, assume weekly
            
            # Calculate category preferences with temporal weighting
            temporal_categories = defaultdict(float)
            for product_name, weight in temporal_items.items():
                if product_name in self.item_categories:
                    temporal_categories[self.item_categories[product_name]] += weight
            
            # Normalize category preferences
            total_weight = sum(temporal_categories.values())
            if total_weight > 0:
                temporal_categories = {cat: weight/total_weight for cat, weight in temporal_categories.items()}
            
            self.user_temporal_profiles[user_id] = {
                'temporal_items': dict(temporal_items),
                'avg_quantities': avg_quantities,
                'purchase_frequencies': purchase_frequencies,
                'temporal_category_preferences': dict(temporal_categories),
                'last_purchase_date': user_orders['delivery_date'].max(),
                'total_temporal_weight': sum(temporal_items.values())
            }
        
        print(f"   ✅ Built temporal profiles for {len(self.user_temporal_profiles):,} users")
    
    def _build_item_seasonality_patterns(self, all_orders):
        """Build seasonality patterns for items based on historical data"""
        print(f"   📅 Building item seasonality patterns...")
        
        self.item_seasonality_patterns = {}
        
        # Add month and day of year to orders
        all_orders['month'] = all_orders['delivery_date'].dt.month
        all_orders['day_of_year'] = all_orders['delivery_date'].dt.dayofyear
        
        for product_name, product_orders in all_orders.groupby('product_name'):
            # Monthly distribution
            monthly_counts = product_orders['month'].value_counts().sort_index()
            monthly_distribution = monthly_counts / monthly_counts.sum()
            
            # Calculate seasonality score (higher = more seasonal)
            seasonality_score = monthly_distribution.std()
            
            # Peak months (top 3)
            peak_months = monthly_distribution.nlargest(3).index.tolist()
            
            # Recent trend (last 30 days vs previous 30 days)
            recent_30_days = product_orders[product_orders['days_ago'] <= 30]
            previous_30_days = product_orders[(product_orders['days_ago'] > 30) & (product_orders['days_ago'] <= 60)]
            
            recent_trend = len(recent_30_days) / max(1, len(previous_30_days))
            
            self.item_seasonality_patterns[product_name] = {
                'monthly_distribution': monthly_distribution.to_dict(),
                'seasonality_score': seasonality_score,
                'peak_months': peak_months,
                'recent_trend': recent_trend,
                'total_orders': len(product_orders)
            }
        
        print(f"   ✅ Built seasonality patterns for {len(self.item_seasonality_patterns):,} items")
    
    def _build_user_purchase_cycles(self, all_orders, current_date):
        """Build user purchase cycle patterns"""
        print(f"   🔄 Building user purchase cycles...")
        
        self.user_purchase_cycles = {}
        
        for user_id, user_orders in all_orders.groupby('customer_id'):
            user_cycles = {}
            
            for product_name, product_orders in user_orders.groupby('product_name'):
                if len(product_orders) >= 2:  # Need at least 2 purchases to calculate cycle
                    # Sort by date
                    sorted_orders = product_orders.sort_values('delivery_date')
                    
                    # Calculate intervals between purchases
                    intervals = []
                    for i in range(1, len(sorted_orders)):
                        interval = (sorted_orders.iloc[i]['delivery_date'] - 
                                  sorted_orders.iloc[i-1]['delivery_date']).days
                        intervals.append(interval)
                    
                    if intervals:
                        avg_cycle = np.mean(intervals)
                        cycle_std = np.std(intervals) if len(intervals) > 1 else 0
                        
                        # Days since last purchase
                        days_since_last = (current_date - sorted_orders['delivery_date'].max()).days
                        
                        # Predict next purchase probability based on cycle
                        if avg_cycle > 0:
                            cycle_position = days_since_last / avg_cycle
                            # Higher probability as we approach the expected cycle time
                            next_purchase_probability = min(1.0, max(0.0, cycle_position))
                        else:
                            next_purchase_probability = 0.5
                        
                        user_cycles[product_name] = {
                            'avg_cycle_days': avg_cycle,
                            'cycle_std': cycle_std,
                            'days_since_last': days_since_last,
                            'next_purchase_probability': next_purchase_probability,
                            'purchase_count': len(sorted_orders)
                        }
            
            if user_cycles:
                self.user_purchase_cycles[user_id] = user_cycles
        
        print(f"   ✅ Built purchase cycles for {len(self.user_purchase_cycles):,} users")

    def precompute_static_features(self, customer_orders_df, catalogue_df):
        """Pre-compute static features (same as original system)"""
        print(f"🔧 Pre-computing static features using '{self.product_name_column}' column...")

        # Validate column exists
        if self.product_name_column not in catalogue_df.columns:
            raise ValueError(f"Column '{self.product_name_column}' not found in catalogue")

        # Create clean catalogue
        clean_catalogue = catalogue_df.drop_duplicates(subset=[self.product_name_column]).copy()

        # Create mappings
        for _, row in catalogue_df.iterrows():
            product_name = row[self.product_name_column]
            sku_code = row['sku_code']

            self.sku_to_name_mapping[sku_code] = product_name

            if product_name not in self.name_to_sku_mapping:
                self.name_to_sku_mapping[product_name] = []
            self.name_to_sku_mapping[product_name].append(sku_code)

        # Convert orders to use product names
        customer_orders_with_names = customer_orders_df.copy()
        customer_orders_with_names['product_name'] = customer_orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        customer_orders_with_names = customer_orders_with_names.dropna(subset=['product_name'])

        # Item categories
        self.item_categories = dict(zip(clean_catalogue[self.product_name_column], clean_catalogue['category_name']))

        # Catalogue lookup
        self.catalogue_lookup = {}
        for _, row in clean_catalogue.iterrows():
            product_name = row[self.product_name_column]
            self.catalogue_lookup[product_name] = {
                'name': product_name,
                'category_name': row['category_name'],
                'sku_codes': self.name_to_sku_mapping[product_name],
                'original_name': row.get('name', product_name),
                'parent_name': row.get('sku_parent_name', product_name)
            }

        # Global statistics
        total_orders = customer_orders_with_names['display_order_id'].nunique()
        name_counts = customer_orders_with_names.groupby('product_name').size()
        self.item_purchase_frequency = (name_counts / total_orders).to_dict()

        # Average quantities
        self.item_avg_quantities = customer_orders_with_names.groupby('product_name')['ordered_qty'].mean().to_dict()

        # Popular items
        name_user_counts = customer_orders_with_names.groupby('product_name')['customer_id'].nunique()
        self.popular_items = name_user_counts[name_user_counts >= 3].index.tolist()

        print(f"✅ Pre-computed {len(self.item_categories)} product categories")
        print(f"✅ Pre-computed {len(self.popular_items)} popular products")
        print(f"✅ Pre-computed mappings: {len(self.name_to_sku_mapping)} products → {sum(len(skus) for skus in self.name_to_sku_mapping.values())} SKUs")

    def get_temporal_recommendations(self, user_id, current_date, top_n=10, diversification_factor=0.3):
        """
        Generate temporal-aware recommendations for a user

        Args:
            user_id: User ID
            current_date: Current date for temporal calculations
            top_n: Number of recommendations to return
            diversification_factor: Factor for recommendation diversification (0-1)

        Returns:
            list: Temporal-aware recommendations
        """
        if user_id not in self.user_temporal_profiles:
            return self._get_fallback_recommendations(user_id, top_n)

        user_profile = self.user_temporal_profiles[user_id]
        user_cycles = self.user_purchase_cycles.get(user_id, {})
        user_repurchase_ratio = self.get_user_repurchase_ratio(user_id)

        # Calculate recommendation targets
        repurchase_count = max(1, int(top_n * user_repurchase_ratio))
        discovery_count = max(1, top_n - repurchase_count)

        all_recommendations = {}

        # 🕒 ENHANCED TEMPORAL REPURCHASE RECOMMENDATIONS WITH DATE-BASED VARIATION
        repurchase_scores = {}

        # Create date-based variation seed
        date_seed = int(current_date.strftime('%Y%m%d'))
        np.random.seed(date_seed)

        for product_name, temporal_weight in user_profile['temporal_items'].items():
            # Base temporal score
            base_score = temporal_weight

            # Cycle-based boost with temporal variation
            cycle_boost = 1.0
            if product_name in user_cycles:
                cycle_info = user_cycles[product_name]
                # Add date-based cycle variation
                days_since_last = cycle_info['days_since_last']
                avg_cycle = cycle_info['avg_cycle_days']

                # Calculate cycle position (0-1 where 1 = due for repurchase)
                if avg_cycle > 0:
                    cycle_position = min(1.0, days_since_last / avg_cycle)
                    # Add temporal variation based on current date
                    date_variation = 0.8 + 0.4 * np.sin(2 * np.pi * (current_date.dayofyear / 365.0))
                    cycle_boost = 1 + (cycle_position * date_variation)
                else:
                    cycle_boost = 1.0

            # Enhanced seasonality boost with day-of-year variation
            seasonality_boost = 1.0
            if product_name in self.item_seasonality_patterns:
                current_month = current_date.month
                current_day_of_year = current_date.dayofyear
                seasonality_info = self.item_seasonality_patterns[product_name]
                monthly_dist = seasonality_info['monthly_distribution']

                # Monthly seasonality
                if current_month in monthly_dist:
                    monthly_boost = 1 + monthly_dist[current_month]
                else:
                    monthly_boost = 1.0

                # Day-of-year variation (creates different patterns across days)
                day_variation = 0.9 + 0.2 * np.sin(2 * np.pi * current_day_of_year / 365.0)

                # Recent trend boost
                trend_boost = seasonality_info['recent_trend']

                seasonality_boost = monthly_boost * day_variation * trend_boost

            # Frequency boost with temporal decay
            frequency_boost = 1.0
            if product_name in user_profile['purchase_frequencies']:
                base_frequency = user_profile['purchase_frequencies'][product_name]
                # Add temporal variation to frequency
                frequency_variation = 0.8 + 0.4 * np.cos(2 * np.pi * (current_date.dayofyear / 365.0))
                frequency_boost = 1 + (base_frequency * frequency_variation / 10)

            # Date-based randomization for diversification
            if diversification_factor > 0:
                # Add controlled randomness based on date and product
                product_hash = hash(product_name + str(date_seed)) % 1000
                random_factor = 0.9 + 0.2 * (product_hash / 1000.0)  # 0.9 to 1.1 range
            else:
                random_factor = 1.0

            # Combined score with all temporal factors
            final_score = base_score * cycle_boost * seasonality_boost * frequency_boost * random_factor
            repurchase_scores[product_name] = final_score

        # Sort and select top repurchase items
        sorted_repurchase = sorted(repurchase_scores.items(), key=lambda x: x[1], reverse=True)

        for i, (product_name, score) in enumerate(sorted_repurchase[:repurchase_count]):
            if product_name in all_recommendations:
                continue

            # Normalize score
            normalized_score = 0.9 - (i * 0.02)
            normalized_score = max(0.5, normalized_score)

            qty = user_profile['avg_quantities'].get(product_name, self.item_avg_quantities.get(product_name, 1.0))
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'

            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'temporal_repurchase',
                'temporal_score': round(score, 4),
                'cycle_probability': user_cycles.get(product_name, {}).get('next_purchase_probability', 0.0)
            }

        # 🕒 ENHANCED TEMPORAL DISCOVERY RECOMMENDATIONS WITH DATE VARIATION
        discovery_scores = defaultdict(float)

        # Category-based discovery with enhanced temporal preferences
        for category, preference in user_profile['temporal_category_preferences'].items():
            if preference > 0.1:
                for product_name in self.popular_items:
                    if (product_name not in user_profile['temporal_items'] and
                        product_name not in all_recommendations and
                        product_name in self.item_categories and
                        self.item_categories[product_name] == category):

                        # Base category preference score
                        base_score = preference * self.item_purchase_frequency.get(product_name, 0)

                        # Enhanced seasonality boost for discovery with day-of-year variation
                        seasonality_boost = 1.0
                        if product_name in self.item_seasonality_patterns:
                            current_month = current_date.month
                            current_day_of_year = current_date.dayofyear
                            seasonality_info = self.item_seasonality_patterns[product_name]
                            monthly_dist = seasonality_info['monthly_distribution']

                            # Monthly seasonality (higher boost for discovery)
                            if current_month in monthly_dist:
                                monthly_boost = 1 + monthly_dist[current_month] * 3  # Higher boost for discovery
                            else:
                                monthly_boost = 1.0

                            # Day-of-year variation for discovery (different pattern than repurchase)
                            discovery_day_variation = 0.7 + 0.6 * np.cos(2 * np.pi * current_day_of_year / 365.0 + np.pi/4)

                            # Recent trend boost
                            trend_boost = seasonality_info['recent_trend']

                            seasonality_boost = monthly_boost * discovery_day_variation * trend_boost

                        # Date-based discovery variation
                        if diversification_factor > 0:
                            # Different randomization pattern for discovery items
                            discovery_hash = hash(product_name + category + str(date_seed)) % 1000
                            discovery_random_factor = 0.8 + 0.4 * (discovery_hash / 1000.0)  # 0.8 to 1.2 range
                        else:
                            discovery_random_factor = 1.0

                        # Weekly rotation factor (changes recommendations based on week)
                        week_of_year = current_date.isocalendar()[1]
                        weekly_rotation = 0.9 + 0.2 * np.sin(2 * np.pi * week_of_year / 52.0)

                        final_discovery_score = base_score * seasonality_boost * discovery_random_factor * weekly_rotation * 100
                        discovery_scores[product_name] += final_discovery_score

        # Sort and select discovery items
        sorted_discovery = sorted(discovery_scores.items(), key=lambda x: x[1], reverse=True)

        for i, (product_name, score) in enumerate(sorted_discovery[:discovery_count]):
            if product_name in all_recommendations:
                continue

            normalized_score = 0.8 - (i * 0.03)
            normalized_score = max(0.3, normalized_score)

            qty = self.item_avg_quantities.get(product_name, 1.0)
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'

            all_recommendations[product_name] = {
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(normalized_score, 4),
                'predicted_quantity': round(qty, 3),
                'recommendation_type': 'temporal_discovery',
                'temporal_score': round(score, 4),
                'cycle_probability': 0.0
            }

        # Apply diversification if requested
        final_recommendations = list(all_recommendations.values())
        if diversification_factor > 0:
            final_recommendations = self._apply_diversification(final_recommendations, diversification_factor, current_date)

        # Sort by score and return
        final_recommendations.sort(key=lambda x: x['score'], reverse=True)
        return final_recommendations[:top_n]

    def _apply_diversification(self, recommendations, diversification_factor, current_date):
        """
        Apply enhanced diversification to recommendations to ensure variety across time periods

        Args:
            recommendations: List of recommendation dictionaries
            diversification_factor: Factor for diversification (0-1)
            current_date: Current date for temporal calculations

        Returns:
            list: Diversified recommendations
        """
        if len(recommendations) <= 1:
            return recommendations

        # Create diversification hash based on current date
        date_seed = int(current_date.strftime('%Y%m%d'))
        np.random.seed(date_seed)

        # Separate by recommendation type
        repurchase_recs = [r for r in recommendations if r['recommendation_type'] == 'temporal_repurchase']
        discovery_recs = [r for r in recommendations if r['recommendation_type'] == 'temporal_discovery']

        # Apply diversification to BOTH repurchase and discovery items
        if len(repurchase_recs) > 2:
            # For repurchase items, apply rotation based on day of week
            day_of_week = current_date.weekday()  # 0=Monday, 6=Sunday

            # Rotate the order of repurchase items based on day
            rotation_amount = day_of_week % len(repurchase_recs)
            repurchase_recs = repurchase_recs[rotation_amount:] + repurchase_recs[:rotation_amount]

            # Apply score variation to create different rankings
            for i, rec in enumerate(repurchase_recs):
                # Day-based score variation
                day_variation = 0.95 + 0.1 * np.sin(2 * np.pi * day_of_week / 7.0 + i)
                rec['score'] *= day_variation

        # Enhanced diversification for discovery items
        if len(discovery_recs) > 1:
            # More aggressive shuffling for discovery items
            shuffle_count = max(1, int(len(discovery_recs) * diversification_factor * 2))  # Double the shuffling

            if shuffle_count < len(discovery_recs):
                # Keep some top items, shuffle the rest more aggressively
                stable_count = max(1, len(discovery_recs) - shuffle_count)
                stable_items = discovery_recs[:stable_count]
                shuffleable_items = discovery_recs[stable_count:]

                # Apply more variation to shuffleable items
                for i, rec in enumerate(shuffleable_items):
                    # Date and position-based variation
                    variation_factor = 0.8 + 0.4 * np.random.random()  # 0.8 to 1.2 range
                    rec['score'] *= variation_factor

                # Re-sort shuffleable items after score variation
                shuffleable_items.sort(key=lambda x: x['score'], reverse=True)
                discovery_recs = stable_items + shuffleable_items
            else:
                # Shuffle all discovery items
                for rec in discovery_recs:
                    variation_factor = 0.8 + 0.4 * np.random.random()
                    rec['score'] *= variation_factor
                discovery_recs.sort(key=lambda x: x['score'], reverse=True)

        return repurchase_recs + discovery_recs

    def _get_fallback_recommendations(self, user_id, top_n):
        """Get fallback recommendations for users without temporal profiles"""
        fallback_recs = []

        # Use popular items as fallback
        for i, product_name in enumerate(self.popular_items[:top_n]):
            item_info = self.catalogue_lookup.get(product_name, {'name': 'Unknown', 'category_name': 'Unknown', 'sku_codes': []})
            sku_code = item_info['sku_codes'][0] if item_info['sku_codes'] else 'UNKNOWN'

            fallback_recs.append({
                'sku_code': sku_code,
                'product_name': product_name,
                'category': item_info['category_name'],
                'score': round(0.5 - (i * 0.02), 4),
                'predicted_quantity': round(self.item_avg_quantities.get(product_name, 1.0), 3),
                'recommendation_type': 'fallback',
                'temporal_score': 0.0,
                'cycle_probability': 0.0
            })

        return fallback_recs

    def analyze_temporal_diversity(self, user_id, date_range_days=7):
        """
        Analyze recommendation diversity across a date range

        Args:
            user_id: User ID to analyze
            date_range_days: Number of days to analyze

        Returns:
            dict: Diversity analysis results
        """
        if user_id not in self.user_temporal_profiles:
            return {'error': 'User not found in temporal profiles'}

        # Get current date from user profile
        current_date = self.user_temporal_profiles[user_id]['last_purchase_date']

        # Generate recommendations for each day in range
        daily_recommendations = {}
        all_recommended_items = set()

        for i in range(date_range_days):
            test_date = current_date + timedelta(days=i)
            recs = self.get_temporal_recommendations(user_id, test_date, top_n=5)

            daily_recs = [rec['product_name'] for rec in recs]
            daily_recommendations[test_date.strftime('%Y-%m-%d')] = daily_recs
            all_recommended_items.update(daily_recs)

        # Calculate diversity metrics
        total_unique_items = len(all_recommended_items)
        total_recommendations = date_range_days * 5  # 5 recs per day

        # Calculate day-to-day similarity
        similarities = []
        dates = list(daily_recommendations.keys())
        for i in range(1, len(dates)):
            prev_recs = set(daily_recommendations[dates[i-1]])
            curr_recs = set(daily_recommendations[dates[i]])

            intersection = len(prev_recs.intersection(curr_recs))
            union = len(prev_recs.union(curr_recs))
            similarity = intersection / union if union > 0 else 0
            similarities.append(similarity)

        avg_similarity = np.mean(similarities) if similarities else 0
        diversity_score = 1 - avg_similarity  # Higher is more diverse

        return {
            'user_id': user_id,
            'date_range_days': date_range_days,
            'total_unique_items': total_unique_items,
            'total_recommendations': total_recommendations,
            'diversity_ratio': total_unique_items / total_recommendations,
            'avg_day_to_day_similarity': avg_similarity,
            'diversity_score': diversity_score,
            'daily_recommendations': daily_recommendations,
            'recommendation_overlap': {
                'min_similarity': min(similarities) if similarities else 0,
                'max_similarity': max(similarities) if similarities else 0,
                'std_similarity': np.std(similarities) if similarities else 0
            }
        }

    def get_temporal_insights(self, user_id):
        """
        Get temporal insights for a user

        Args:
            user_id: User ID

        Returns:
            dict: Temporal insights
        """
        if user_id not in self.user_temporal_profiles:
            return {'error': 'User not found in temporal profiles'}

        user_profile = self.user_temporal_profiles[user_id]
        user_cycles = self.user_purchase_cycles.get(user_id, {})

        # Top temporal items
        top_temporal_items = sorted(user_profile['temporal_items'].items(),
                                  key=lambda x: x[1], reverse=True)[:10]

        # Items due for repurchase
        due_items = []
        for item, cycle_info in user_cycles.items():
            if cycle_info['next_purchase_probability'] > 0.7:
                due_items.append({
                    'item': item,
                    'probability': cycle_info['next_purchase_probability'],
                    'days_since_last': cycle_info['days_since_last'],
                    'avg_cycle': cycle_info['avg_cycle_days']
                })

        due_items.sort(key=lambda x: x['probability'], reverse=True)

        # Category preferences
        top_categories = sorted(user_profile['temporal_category_preferences'].items(),
                              key=lambda x: x[1], reverse=True)[:5]

        return {
            'user_id': user_id,
            'temporal_profile_summary': {
                'total_temporal_weight': user_profile['total_temporal_weight'],
                'unique_items': len(user_profile['temporal_items']),
                'last_purchase_date': user_profile['last_purchase_date'].strftime('%Y-%m-%d'),
                'avg_purchase_frequency': np.mean(list(user_profile['purchase_frequencies'].values())) if user_profile['purchase_frequencies'] else 0
            },
            'top_temporal_items': top_temporal_items[:5],
            'items_due_for_repurchase': due_items[:5],
            'top_categories': top_categories,
            'purchase_cycles_tracked': len(user_cycles)
        }
