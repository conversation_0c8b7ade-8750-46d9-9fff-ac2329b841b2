#!/usr/bin/env python3
"""
🔥 HYBRID ML-ENHANCED RECOMMENDATION SYSTEM

Root Cause Analysis of Advanced ML Failure:
1. Complex ensemble diluted strong signals (0.4% vs 30.3%)
2. ML techniques generated too diverse recommendations
3. Lost connection to actual user behavior patterns

NEW HYBRID APPROACH:
1. START with Enhanced Temporal System (30.3% precision) as STRONG BASE
2. ADD selective ML enhancements that AMPLIFY existing signals
3. Use ML for REFINEMENT, not replacement
4. Focus on PROVEN patterns with ML optimization
5. Maintain signal strength while adding intelligence

STRATEGY: Best of both worlds - proven performance + ML intelligence
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
from final_temporal_recommendation_system import FinalTemporalRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

# ML imports with fallbacks
try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

class HybridMLEnhancedSystem(FinalTemporalRecommendationSystem):
    """Hybrid system combining proven temporal approach with selective ML enhancements"""
    
    def __init__(self, user_repurchase_ratios_df=None, product_name_column='name'):
        # Initialize with proven enhanced temporal system as base
        super().__init__(user_repurchase_ratios_df, product_name_column)
        
        print(f"🔥 HYBRID ML-ENHANCED SYSTEM INITIALIZED")
        print(f"🎯 STRATEGY: Enhanced Temporal (30.3%) + Selective ML Intelligence")
        print(f"📊 APPROACH: Amplify proven signals with ML, don't replace them")
        
        # Selective ML enhancements
        self.ml_user_clusters = {}
        self.ml_product_similarities = {}
        self.ml_sequence_boosts = {}
        self.ml_confidence_scores = {}
        
    def build_hybrid_ml_features(self, customer_orders_df, catalogue_df):
        """Build selective ML features that enhance proven temporal patterns"""
        print(f"🔧 BUILDING HYBRID ML ENHANCEMENTS...")
        
        # First build proven enhanced temporal features
        self.precompute_static_features(customer_orders_df, catalogue_df)
        
        # Then add selective ML enhancements
        self._build_ml_user_clustering(customer_orders_df)
        self._build_ml_product_similarities(customer_orders_df)
        self._build_ml_sequence_intelligence(customer_orders_df)
        self._build_ml_confidence_scoring(customer_orders_df)
        
        print(f"✅ Hybrid ML enhancements built successfully")
    
    def _build_ml_user_clustering(self, customer_orders_df):
        """Build ML user clustering to enhance segment-based strategies"""
        print(f"   👥 Building ML user clustering...")
        
        if not SKLEARN_AVAILABLE:
            print(f"      ⚠️ Scikit-learn not available - using simple clustering")
            self._build_simple_user_clustering(customer_orders_df)
            return
        
        # Prepare user features for clustering
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        user_features = []
        user_ids = []
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            # Feature engineering for clustering
            total_orders = len(customer_orders)
            unique_products = customer_orders['product_name'].nunique()
            avg_quantity = customer_orders['ordered_qty'].mean()
            
            # Category distribution
            categories = customer_orders.merge(
                pd.DataFrame(list(self.item_categories.items()), columns=['product_name', 'category_name']),
                on='product_name', how='left'
            )['category_name'].value_counts(normalize=True)
            
            # Create feature vector
            features = [
                total_orders,
                unique_products,
                avg_quantity,
                unique_products / total_orders if total_orders > 0 else 0,  # Diversity
                categories.get('Vegetables', 0),
                categories.get('Fruits', 0),
                categories.get('Exotic', 0)
            ]
            
            user_features.append(features)
            user_ids.append(customer_id)
        
        if len(user_features) > 10:
            # Standardize features
            scaler = StandardScaler()
            user_features_scaled = scaler.fit_transform(user_features)
            
            # Cluster users
            n_clusters = min(5, len(user_features) // 20)  # Reasonable number of clusters
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(user_features_scaled)
            
            # Store cluster assignments
            for user_id, cluster in zip(user_ids, clusters):
                self.ml_user_clusters[user_id] = {
                    'cluster': cluster,
                    'cluster_center': kmeans.cluster_centers_[cluster]
                }
        
        print(f"      ✅ ML clustered {len(self.ml_user_clusters)} users into clusters")
    
    def _build_simple_user_clustering(self, customer_orders_df):
        """Simple user clustering fallback"""
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            total_orders = len(customer_orders)
            unique_products = customer_orders['product_name'].nunique()
            
            # Simple clustering based on order patterns
            if total_orders >= 50 and unique_products >= 30:
                cluster = 0  # High activity, high diversity
            elif total_orders >= 20:
                cluster = 1  # Medium activity
            else:
                cluster = 2  # Low activity
            
            self.ml_user_clusters[customer_id] = {'cluster': cluster}
    
    def _build_ml_product_similarities(self, customer_orders_df):
        """Build ML-enhanced product similarities"""
        print(f"   🎯 Building ML product similarities...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Create user-product matrix
        user_product_matrix = orders_with_names.groupby(['customer_id', 'product_name']).size().unstack(fill_value=0)
        
        if SKLEARN_AVAILABLE and len(user_product_matrix.columns) > 1:
            # Calculate product similarities using cosine similarity
            product_similarity_matrix = cosine_similarity(user_product_matrix.T)
            
            # Store top similarities for each product
            for i, product_a in enumerate(user_product_matrix.columns):
                similarities = product_similarity_matrix[i]
                top_similar_indices = np.argsort(similarities)[-11:-1]  # Top 10 similar (excluding self)
                
                similar_products = []
                for idx in top_similar_indices:
                    if similarities[idx] > 0.1:  # Minimum similarity threshold
                        similar_product = user_product_matrix.columns[idx]
                        similarity_score = similarities[idx]
                        similar_products.append((similar_product, similarity_score))
                
                if similar_products:
                    self.ml_product_similarities[product_a] = similar_products
        
        print(f"      ✅ Built ML similarities for {len(self.ml_product_similarities)} products")
    
    def _build_ml_sequence_intelligence(self, customer_orders_df):
        """Build ML sequence intelligence to boost temporal patterns"""
        print(f"   🔄 Building ML sequence intelligence...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Analyze sequence patterns with ML enhancement
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            sorted_orders = customer_orders.sort_values('delivery_date')
            
            # Group by order sessions
            order_sequences = []
            for date, date_orders in sorted_orders.groupby('delivery_date'):
                products = date_orders['product_name'].tolist()
                order_sequences.append(products)
            
            # Build sequence intelligence
            sequence_boosts = {}
            for i in range(len(order_sequences) - 1):
                current_products = set(order_sequences[i])
                next_products = set(order_sequences[i + 1])
                
                # Calculate sequence strength
                for current_product in current_products:
                    for next_product in next_products:
                        if current_product != next_product:
                            key = (current_product, next_product)
                            sequence_boosts[key] = sequence_boosts.get(key, 0) + 1
            
            # Normalize and store significant sequences
            if sequence_boosts:
                max_count = max(sequence_boosts.values())
                significant_sequences = {}
                
                for (current_product, next_product), count in sequence_boosts.items():
                    strength = count / max_count
                    if strength > 0.3:  # Significant sequence threshold
                        if current_product not in significant_sequences:
                            significant_sequences[current_product] = []
                        significant_sequences[current_product].append((next_product, strength))
                
                if significant_sequences:
                    self.ml_sequence_boosts[customer_id] = significant_sequences
        
        print(f"      ✅ Built sequence intelligence for {len(self.ml_sequence_boosts)} users")
    
    def _build_ml_confidence_scoring(self, customer_orders_df):
        """Build ML confidence scoring for recommendations"""
        print(f"   📊 Building ML confidence scoring...")
        
        orders_with_names = customer_orders_df.copy()
        orders_with_names['product_name'] = orders_with_names['sku_code'].map(self.sku_to_name_mapping)
        orders_with_names = orders_with_names.dropna(subset=['product_name'])
        
        # Calculate confidence scores based on user behavior consistency
        for customer_id, customer_orders in orders_with_names.groupby('customer_id'):
            product_frequencies = customer_orders['product_name'].value_counts()
            total_orders = len(customer_orders)
            
            # Calculate confidence metrics
            top_product_share = product_frequencies.iloc[0] / total_orders if len(product_frequencies) > 0 else 0
            product_concentration = len(product_frequencies) / total_orders if total_orders > 0 else 0
            
            # Temporal consistency
            date_range = (customer_orders['delivery_date'].max() - customer_orders['delivery_date'].min()).days
            order_frequency = total_orders / max(1, date_range / 7)
            
            # Overall confidence score
            confidence_score = (
                top_product_share * 0.4 +
                (1 - product_concentration) * 0.3 +  # Lower concentration = higher confidence
                min(1.0, order_frequency / 2) * 0.3
            )
            
            self.ml_confidence_scores[customer_id] = {
                'confidence': confidence_score,
                'top_product_share': top_product_share,
                'order_frequency': order_frequency
            }
        
        print(f"      ✅ Built confidence scores for {len(self.ml_confidence_scores)} users")
    
    def get_hybrid_ml_recommendations(self, user_id, current_date, top_n=8):
        """
        Generate hybrid recommendations combining proven temporal approach with ML intelligence
        
        HYBRID STRATEGY:
        1. Generate base recommendations using proven Enhanced Temporal System (30.3%)
        2. Apply ML enhancements to AMPLIFY and REFINE signals
        3. Use ML clustering for personalized weighting
        4. Apply ML product similarities for discovery
        5. Use ML sequence intelligence for temporal boosts
        6. Apply ML confidence scoring for final ranking
        """
        
        # 1. GET BASE RECOMMENDATIONS from proven Enhanced Temporal System
        base_recommendations = self.get_temporal_recommendations(user_id, current_date, top_n * 2)  # Get more for filtering
        
        if not base_recommendations:
            return []
        
        # 2. APPLY ML ENHANCEMENTS
        enhanced_recommendations = []
        
        for rec in base_recommendations:
            product_name = rec['product_name']
            base_score = rec['score']
            
            # ML Enhancement 1: User cluster personalization
            cluster_boost = self._get_ml_cluster_boost(user_id, product_name)
            
            # ML Enhancement 2: Product similarity expansion
            similarity_boost = self._get_ml_similarity_boost(user_id, product_name)
            
            # ML Enhancement 3: Sequence intelligence boost
            sequence_boost = self._get_ml_sequence_boost(user_id, product_name)
            
            # ML Enhancement 4: Confidence-based weighting
            confidence_weight = self._get_ml_confidence_weight(user_id)
            
            # Combine enhancements (conservative approach)
            ml_enhanced_score = base_score * (
                1.0 +
                cluster_boost * 0.1 +      # 10% max boost from clustering
                similarity_boost * 0.15 +  # 15% max boost from similarities
                sequence_boost * 0.2       # 20% max boost from sequences
            ) * confidence_weight
            
            enhanced_rec = rec.copy()
            enhanced_rec['score'] = ml_enhanced_score
            enhanced_rec['recommendation_type'] = 'hybrid_ml_enhanced'
            enhanced_rec['ml_cluster_boost'] = cluster_boost
            enhanced_rec['ml_similarity_boost'] = similarity_boost
            enhanced_rec['ml_sequence_boost'] = sequence_boost
            enhanced_rec['ml_confidence_weight'] = confidence_weight
            
            enhanced_recommendations.append(enhanced_rec)
        
        # 3. SORT BY ENHANCED SCORES and return top_n
        enhanced_recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        return enhanced_recommendations[:top_n]
    
    def _get_ml_cluster_boost(self, user_id, product_name):
        """Get ML cluster-based boost"""
        if user_id not in self.ml_user_clusters:
            return 0.0
        
        user_cluster = self.ml_user_clusters[user_id]['cluster']
        
        # Simple cluster-based boost (can be enhanced with more sophisticated logic)
        if user_cluster == 0:  # High activity cluster
            return 0.3 if product_name in self.popular_items else 0.1
        elif user_cluster == 1:  # Medium activity cluster
            return 0.2
        else:  # Low activity cluster
            return 0.1
    
    def _get_ml_similarity_boost(self, user_id, product_name):
        """Get ML product similarity boost"""
        if product_name not in self.ml_product_similarities:
            return 0.0
        
        # Check if user has purchased similar products
        if user_id in self.user_temporal_profiles:
            # Handle different temporal profile structures
            if 'products' in self.user_temporal_profiles[user_id]:
                user_products = set(self.user_temporal_profiles[user_id]['products'].keys())
            elif 'temporal_items' in self.user_temporal_profiles[user_id]:
                user_products = set(self.user_temporal_profiles[user_id]['temporal_items'])
            else:
                user_products = set()
            
            similar_products = self.ml_product_similarities[product_name]
            similarity_boost = 0.0
            
            for similar_product, similarity_score in similar_products:
                if similar_product in user_products:
                    similarity_boost += similarity_score * 0.5  # Scale down
            
            return min(0.5, similarity_boost)  # Cap at 50% boost
        
        return 0.0
    
    def _get_ml_sequence_boost(self, user_id, product_name):
        """Get ML sequence intelligence boost"""
        if user_id not in self.ml_sequence_boosts:
            return 0.0
        
        if user_id not in self.user_temporal_profiles:
            return 0.0

        # Check if product follows user's sequence patterns
        # Handle different temporal profile structures
        if 'products' in self.user_temporal_profiles[user_id]:
            user_products = set(self.user_temporal_profiles[user_id]['products'].keys())
        elif 'temporal_items' in self.user_temporal_profiles[user_id]:
            user_products = set(self.user_temporal_profiles[user_id]['temporal_items'])
        else:
            user_products = set()
        sequence_data = self.ml_sequence_boosts[user_id]
        
        sequence_boost = 0.0
        for user_product in user_products:
            if user_product in sequence_data:
                for next_product, strength in sequence_data[user_product]:
                    if next_product == product_name:
                        sequence_boost += strength
        
        return min(0.8, sequence_boost)  # Cap at 80% boost
    
    def _get_ml_confidence_weight(self, user_id):
        """Get ML confidence-based weight"""
        if user_id not in self.ml_confidence_scores:
            return 1.0
        
        confidence_data = self.ml_confidence_scores[user_id]
        confidence = confidence_data['confidence']
        
        # Higher confidence users get more weight
        return 0.8 + (confidence * 0.4)  # Range: 0.8 to 1.2

    def calculate_precision_recall_hybrid_ml(self, customer_orders_df, test_start_date, test_end_date):
        """Calculate precision and recall for hybrid ML-enhanced system"""
        print(f"📊 CALCULATING HYBRID ML-ENHANCED PRECISION & RECALL")
        print(f"   📅 Test period: {test_start_date.strftime('%Y-%m-%d')} to {test_end_date.strftime('%Y-%m-%d')}")
        print(f"   🔥 Using hybrid approach: Enhanced Temporal (30.3%) + Selective ML")

        # Split data
        train_data = customer_orders_df[customer_orders_df['delivery_date'] < test_start_date]

        # Test data: orders placed during test period
        test_delivery_start = test_start_date + timedelta(days=1)
        test_delivery_end = test_end_date + timedelta(days=1)
        test_data = customer_orders_df[
            (customer_orders_df['delivery_date'] >= test_delivery_start) &
            (customer_orders_df['delivery_date'] <= test_delivery_end)
        ]

        print(f"   📊 Train orders: {len(train_data):,}")
        print(f"   📊 Test orders: {len(test_data):,}")

        # Build hybrid ML features on training data
        catalogue_df = pd.read_csv('catalogue_rc_with_parent_names.csv')
        self.build_hybrid_ml_features(train_data, catalogue_df)

        # Extract temporal patterns for base system
        self.extract_temporal_patterns(train_data, test_start_date)

        # Convert test data
        test_data_with_names = test_data.copy()
        test_data_with_names['product_name'] = test_data_with_names['sku_code'].map(self.sku_to_name_mapping)
        test_data_with_names = test_data_with_names.dropna(subset=['product_name'])
        test_data_with_names['order_date'] = test_data_with_names['delivery_date'] - timedelta(days=1)

        # Group actual orders by order_date
        actual_orders_by_date = {}
        for _, order in test_data_with_names.iterrows():
            order_date = order['order_date']
            user_id = order['customer_id']
            product_name = order['product_name']

            if order_date not in actual_orders_by_date:
                actual_orders_by_date[order_date] = {}

            if user_id not in actual_orders_by_date[order_date]:
                actual_orders_by_date[order_date][user_id] = set()

            actual_orders_by_date[order_date][user_id].add(product_name)

        # Calculate metrics
        precision_scores = []
        recall_scores = []
        f1_scores = []

        ml_boost_effectiveness = []

        test_dates = [test_start_date + timedelta(days=i) for i in range((test_end_date - test_start_date).days + 1)]

        total_comparisons = 0
        for test_date in test_dates:
            if test_date not in actual_orders_by_date:
                continue

            users_with_orders = actual_orders_by_date[test_date]
            print(f"   📅 Testing {test_date.strftime('%Y-%m-%d')}: {len(users_with_orders)} users")

            for user_id, actual_items in users_with_orders.items():
                # Generate hybrid ML-enhanced recommendations
                dynamic_k = self.calculate_dynamic_k_for_user(user_id, train_data)
                recommendations = self.get_hybrid_ml_recommendations(user_id, test_date, dynamic_k)
                recommended_items = set([rec['product_name'] for rec in recommendations])

                # Calculate metrics
                if len(recommended_items) > 0:
                    precision = len(recommended_items.intersection(actual_items)) / len(recommended_items)
                else:
                    precision = 0.0

                if len(actual_items) > 0:
                    recall = len(recommended_items.intersection(actual_items)) / len(actual_items)
                else:
                    recall = 0.0

                if precision + recall > 0:
                    f1 = 2 * (precision * recall) / (precision + recall)
                else:
                    f1 = 0.0

                precision_scores.append(precision)
                recall_scores.append(recall)
                f1_scores.append(f1)

                # Track ML boost effectiveness
                if recommendations:
                    avg_ml_boost = np.mean([
                        rec.get('ml_cluster_boost', 0) +
                        rec.get('ml_similarity_boost', 0) +
                        rec.get('ml_sequence_boost', 0)
                        for rec in recommendations
                    ])
                    ml_boost_effectiveness.append((precision, avg_ml_boost))

                total_comparisons += 1

        # Calculate overall metrics
        avg_precision = np.mean(precision_scores) if precision_scores else 0.0
        avg_recall = np.mean(recall_scores) if recall_scores else 0.0
        avg_f1 = np.mean(f1_scores) if f1_scores else 0.0

        # Analyze ML boost effectiveness
        ml_boost_correlation = 0.0
        if ml_boost_effectiveness:
            precisions, boosts = zip(*ml_boost_effectiveness)
            if len(set(boosts)) > 1:  # Avoid division by zero
                ml_boost_correlation = np.corrcoef(precisions, boosts)[0, 1]

        print(f"✅ HYBRID ML-ENHANCED PRECISION & RECALL RESULTS:")
        print(f"   📊 Overall Average Precision: {avg_precision:.4f}")
        print(f"   📊 Overall Average Recall: {avg_recall:.4f}")
        print(f"   📊 Overall Average F1-Score: {avg_f1:.4f}")
        print(f"   👥 User-date combinations tested: {total_comparisons}")

        print(f"\n🔥 HYBRID ML ENHANCEMENT ANALYSIS:")
        print(f"   🤖 ML boost correlation with precision: {ml_boost_correlation:.4f}")
        print(f"   📊 Users with ML clustering: {len(self.ml_user_clusters)}")
        print(f"   🎯 Products with ML similarities: {len(self.ml_product_similarities)}")
        print(f"   🔄 Users with sequence intelligence: {len(self.ml_sequence_boosts)}")
        print(f"   📈 Users with confidence scoring: {len(self.ml_confidence_scores)}")

        print(f"   🚀 Hybrid approach: Enhanced Temporal base + Selective ML enhancements")

        return {
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'avg_f1': avg_f1,
            'num_users_tested': total_comparisons,
            'precision_scores': precision_scores,
            'recall_scores': recall_scores,
            'f1_scores': f1_scores,
            'ml_boost_correlation': ml_boost_correlation,
            'ml_features_count': {
                'user_clusters': len(self.ml_user_clusters),
                'product_similarities': len(self.ml_product_similarities),
                'sequence_intelligence': len(self.ml_sequence_boosts),
                'confidence_scores': len(self.ml_confidence_scores)
            },
            'enhancement_type': 'hybrid_ml_enhanced'
        }
